[supervisord]
nodaemon=true
logfile=/var/log/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/var/run/supervisord.pid
user=root

[program:WeKnora]
command=/app/WeKnora
directory=/app
autostart=true
autorestart=true
startretries=5
redirect_stderr=true
stdout_logfile=/var/log/WeKnora.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=CGO_ENABLED=1
user=appuser

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock 