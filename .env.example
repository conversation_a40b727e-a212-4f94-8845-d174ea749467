# 使用说明
# 1. 复制此文件为 .env
# 2. 替换所有占位符为实际值
# 3. 确保 .env 文件不会被提交到版本控制系统 

# gin mod
# 可选值: debug(开发模式，有详细日志), release(生产模式)
GIN_MODE=debug

# ollama
# Ollama 服务的基准 URL，用于连接本地运行的 Ollama LLM 服务
OLLAMA_BASE_URL=http://host.docker.internal:11435

# 存储配置
# 主数据库类型(postgres/mysql)
DB_DRIVER=postgres

# 向量存储类型(postgres/elasticsearch_v7/elasticsearch_v8)
RETRIEVE_DRIVER=postgres

# 文件存储类型(local/minio/cos)
STORAGE_TYPE=local

# 流处理后端(memory/redis)
STREAM_MANAGER_TYPE=redis

# 主数据库配置
# 数据库地址，可以是主机名或IP地址
DB_HOST=postgres

# 数据库端口，默认为5432
DB_PORT=5432

# 数据库用户名
DB_USER=postgres

# 数据库密码
DB_PASSWORD=postgres123!@#

# 数据库名称
DB_NAME=WeKnora

# 如果使用 redis 作为流处理后端，需要配置以下参数
# Redis地址，可以是主机名或IP地址
REDIS_HOST=redis

# Redis端口，默认为6379
REDIS_PORT=6379

# Redis密码，如果没有设置密码，可以留空
REDIS_PASSWORD=redis123!@#

# Redis数据库索引，默认为0
REDIS_DB=0

# Redis key的前缀，用于命名空间隔离
REDIS_PREFIX=stream:

# 当使用本地存储时，文件保存的基础目录路径
LOCAL_STORAGE_BASE_DIR=./data/files

TENANT_AES_KEY=weknorarag-api-key-secret-secret

# 是否开启知识图谱构建和检索（构建阶段需调用大模型，耗时较长）
ENABLE_GRAPH_RAG=false

# 如果使用ElasticSearch作为向量存储，需要配置以下参数
# ElasticSearch地址，例如 http://localhost:9200
# ELASTICSEARCH_ADDR=your_elasticsearch_addr

# ElasticSearch用户名，如果需要身份验证
# ELASTICSEARCH_USERNAME=your_elasticsearch_username

# ElasticSearch密码，如果需要身份验证
# ELASTICSEARCH_PASSWORD=your_elasticsearch_password

# ElasticSearch索引名称，用于存储向量数据
# ELASTICSEARCH_INDEX=WeKnora

# 如果使用MinIO作为文件存储，需要配置以下参数
# MinIO服务器地址，例如 minio:9000
# MINIO_ENDPOINT=minio:9000

# MinIO访问密钥
# MINIO_ACCESS_KEY_ID=your_minio_access_key

# MinIO密钥
# MINIO_SECRET_ACCESS_KEY=your_minio_secret_key

# MinIO桶名称，用于存储文件
# MINIO_BUCKET_NAME=your_minio_bucket_name

# 如果使用腾讯云COS作为文件存储，需要配置以下参数
# 腾讯云COS的访问密钥ID
# COS_SECRET_ID=your_cos_secret_id

# 腾讯云COS的密钥
# COS_SECRET_KEY=your_cos_secret_key

# 腾讯云COS的区域，例如 ap-guangzhou
# COS_REGION=your_cos_region

# 腾讯云COS的桶名称
# COS_BUCKET_NAME=your_cos_bucket_name

# 腾讯云COS的应用ID
# COS_APP_ID=your_cos_app_id

# 腾讯云COS的路径前缀，用于存储文件
# COS_PATH_PREFIX=your_cos_path_prefix

# COS_ENABLE_OLD_DOMAIN=true 表示启用旧的域名格式，默认为 true
COS_ENABLE_OLD_DOMAIN=true

# 初始化默认租户与知识库
# 租户ID，通常是一个字符串
INIT_TEST_TENANT_ID=1

# 知识库ID，通常是一个字符串
INIT_TEST_KNOWLEDGE_BASE_ID=kb-00000001

# LLM Model
# 使用的LLM模型名称
# 默认使用 Ollama 的 Qwen3 8B 模型，ollama 会自动处理模型下载和加载
# 如果需要使用其他模型，请替换为实际的模型名称
INIT_LLM_MODEL_NAME=qwen3:8b

# LLM模型的访问地址
# 支持第三方模型服务的URL
# 如果使用 Ollama 的本地服务，可以留空，ollama 会自动处理
# INIT_LLM_MODEL_BASE_URL=your_llm_model_base_url

# LLM模型的API密钥，如果需要身份验证，可以设置
# 支持第三方模型服务的API密钥
# 如果使用 Ollama 的本地服务，可以留空，ollama 会自动处理
# INIT_LLM_MODEL_API_KEY=your_llm_model_api_key

# Embedding Model
# 使用的Embedding模型名称
# 默认使用 nomic-embed-text 模型，支持文本嵌入
# 如果需要使用其他模型，请替换为实际的模型名称
INIT_EMBEDDING_MODEL_NAME=nomic-embed-text

# Embedding模型向量维度
INIT_EMBEDDING_MODEL_DIMENSION=768

# Embedding模型的ID，通常是一个字符串
INIT_EMBEDDING_MODEL_ID=builtin:nomic-embed-text:768

# Embedding模型的访问地址
# 支持第三方模型服务的URL
# 如果使用 Ollama 的本地服务，可以留空，ollama 会自动处理
# INIT_EMBEDDING_MODEL_BASE_URL=your_embedding_model_base_url

# Embedding模型的API密钥，如果需要身份验证，可以设置
# 支持第三方模型服务的API密钥
# 如果使用 Ollama 的本地服务，可以留空，ollama 会自动处理
# INIT_EMBEDDING_MODEL_API_KEY=your_embedding_model_api_key

# Rerank Model(可选)
# 对于rag来说，使用Rerank模型对提升文档搜索的准确度有着重要作用
# 目前 ollama 暂不支持运行 Rerank 模型
# 使用的Rerank模型名称
# INIT_RERANK_MODEL_NAME=your_rerank_model_name

# Rerank模型的访问地址
# 支持第三方模型服务的URL
# INIT_RERANK_MODEL_BASE_URL=your_rerank_model_base_url

# Rerank模型的API密钥，如果需要身份验证，可以设置
# 支持第三方模型服务的API密钥
# INIT_RERANK_MODEL_API_KEY=your_rerank_model_api_key
