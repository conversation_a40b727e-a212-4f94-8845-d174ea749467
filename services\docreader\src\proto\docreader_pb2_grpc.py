# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import docreader_pb2 as docreader__pb2


class DocReaderStub(object):
    """文档读取服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ReadFromFile = channel.unary_unary(
                '/docreader.DocReader/ReadFromFile',
                request_serializer=docreader__pb2.ReadFromFileRequest.SerializeToString,
                response_deserializer=docreader__pb2.ReadResponse.FromString,
                )
        self.ReadFromURL = channel.unary_unary(
                '/docreader.DocReader/ReadFromURL',
                request_serializer=docreader__pb2.ReadFromURLRequest.SerializeToString,
                response_deserializer=docreader__pb2.ReadResponse.FromString,
                )


class DocReaderServicer(object):
    """文档读取服务
    """

    def ReadFromFile(self, request, context):
        """从文件读取文档
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReadFromURL(self, request, context):
        """从URL读取文档
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DocReaderServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ReadFromFile': grpc.unary_unary_rpc_method_handler(
                    servicer.ReadFromFile,
                    request_deserializer=docreader__pb2.ReadFromFileRequest.FromString,
                    response_serializer=docreader__pb2.ReadResponse.SerializeToString,
            ),
            'ReadFromURL': grpc.unary_unary_rpc_method_handler(
                    servicer.ReadFromURL,
                    request_deserializer=docreader__pb2.ReadFromURLRequest.FromString,
                    response_serializer=docreader__pb2.ReadResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'docreader.DocReader', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DocReader(object):
    """文档读取服务
    """

    @staticmethod
    def ReadFromFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/docreader.DocReader/ReadFromFile',
            docreader__pb2.ReadFromFileRequest.SerializeToString,
            docreader__pb2.ReadResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ReadFromURL(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/docreader.DocReader/ReadFromURL',
            docreader__pb2.ReadFromURLRequest.SerializeToString,
            docreader__pb2.ReadResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
