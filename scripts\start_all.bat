@echo off
echo WeKnora Startup Script for Windows
echo ===================================

:: Set Docker command path
set "DOCKER_CMD=docker"

:: Check if docker is in PATH
where docker >nul 2>&1
if %errorlevel% equ 0 (
    echo INFO: Docker found in PATH
    goto :check_docker_running
)

:: Try common Docker Desktop installation paths
if exist "C:\Program Files\Docker\Docker\resources\bin\docker.exe" (
    set "DOCKER_CMD=C:\Program Files\Docker\Docker\resources\bin\docker.exe"
    echo INFO: Docker found at: %DOCKER_CMD%
    goto :check_docker_running
)

echo ERROR: Docker not found. Please ensure Docker Desktop is installed.
echo.
echo Troubleshooting steps:
echo 1. Make sure Docker Desktop is installed
echo 2. Restart your command prompt
echo 3. Try running this script as Administrator
echo 4. Check if Docker Desktop is running
pause
exit /b 1

:check_docker_running
:: Check if Docker is running
"%DOCKER_CMD%" info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running. Please start Docker Desktop.
    echo.
    echo Please:
    echo 1. Open Docker Desktop
    echo 2. Wait for it to fully start
    echo 3. Try running this script again
    pause
    exit /b 1
)

echo INFO: Docker is running successfully.

echo INFO: Docker environment check passed.

:: Check for .env file
if not exist ".env" (
    echo WARNING: .env file does not exist.
    if exist ".env.example" (
        echo INFO: Creating .env from .env.example
        copy ".env.example" ".env" >nul
        echo SUCCESS: .env file created from template.
    ) else (
        echo ERROR: .env.example template not found.
        pause
        exit /b 1
    )
) else (
    echo INFO: .env file exists.
)

:: Parse command line arguments
set "STOP_SERVICES=false"
if "%1"=="-s" set "STOP_SERVICES=true"
if "%1"=="--stop" set "STOP_SERVICES=true"

if "%STOP_SERVICES%"=="true" (
    echo INFO: Stopping Docker containers...

    :: Disable Docker credential helper to avoid credential issues
    set DOCKER_CONFIG=

    :: Use docker compose (new version)
    "%DOCKER_CMD%" compose down --remove-orphans

    if %errorlevel% equ 0 (
        echo SUCCESS: All Docker containers stopped.
    ) else (
        echo ERROR: Failed to stop Docker containers.
        pause
        exit /b 1
    )
) else (
    echo INFO: Starting Docker containers...

    :: Disable Docker credential helper to avoid credential issues
    set DOCKER_CONFIG=

    :: Use docker compose (new version)
    "%DOCKER_CMD%" compose up --build -d

    if %errorlevel% equ 0 (
        echo SUCCESS: All Docker containers started successfully.
        echo.
        echo Services are available at:
        echo   - Frontend: http://localhost
        echo   - API: http://localhost:8080
        echo   - Jaeger Tracing: http://localhost:16686
        echo.
        echo Container status:
        set DOCKER_CONFIG=
        "%DOCKER_CMD%" compose ps
    ) else (
        echo ERROR: Failed to start Docker containers.
        pause
        exit /b 1
    )
)

pause
