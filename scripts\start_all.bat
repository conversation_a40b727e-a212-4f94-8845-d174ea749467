@echo off
echo We<PERSON><PERSON><PERSON>up Script for Windows
echo ===================================

:: Check if Docker is installed
where docker >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

:: Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running. Please start Docker Desktop.
    pause
    exit /b 1
)

echo INFO: Docker environment check passed.

:: Check for .env file
if not exist ".env" (
    echo WARNING: .env file does not exist.
    if exist ".env.example" (
        echo INFO: Creating .env from .env.example
        copy ".env.example" ".env" >nul
        echo SUCCESS: .env file created from template.
    ) else (
        echo ERROR: .env.example template not found.
        pause
        exit /b 1
    )
) else (
    echo INFO: .env file exists.
)

:: Parse command line arguments
set "STOP_SERVICES=false"
if "%1"=="-s" set "STOP_SERVICES=true"
if "%1"=="--stop" set "STOP_SERVICES=true"

if "%STOP_SERVICES%"=="true" (
    echo INFO: Stopping Docker containers...

    :: Try docker-compose first, then docker compose
    where docker-compose >nul 2>&1
    if %errorlevel% equ 0 (
        docker-compose down --remove-orphans
    ) else (
        docker compose down --remove-orphans
    )

    if %errorlevel% equ 0 (
        echo SUCCESS: All Docker containers stopped.
    ) else (
        echo ERROR: Failed to stop Docker containers.
        pause
        exit /b 1
    )
) else (
    echo INFO: Starting Docker containers...

    :: Try docker-compose first, then docker compose
    where docker-compose >nul 2>&1
    if %errorlevel% equ 0 (
        docker-compose up --build -d
    ) else (
        docker compose up --build -d
    )

    if %errorlevel% equ 0 (
        echo SUCCESS: All Docker containers started successfully.
        echo.
        echo Services are available at:
        echo   - Frontend: http://localhost
        echo   - API: http://localhost:8080
        echo   - Jaeger Tracing: http://localhost:16686
        echo.
        echo Container status:
        where docker-compose >nul 2>&1
        if %errorlevel% equ 0 (
            docker-compose ps
        ) else (
            docker compose ps
        )
    ) else (
        echo ERROR: Failed to start Docker containers.
        pause
        exit /b 1
    )
)

pause
