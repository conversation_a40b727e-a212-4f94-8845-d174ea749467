[supervisord]
nodaemon=true
logfile=/var/log/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/var/run/supervisord.pid
user=root

[program:docreader]
command=/bin/sh -c "python /app/src/server/server.py 2>&1"
directory=/app
autostart=true
autorestart=true
startretries=5
startsecs=5
priority=10
redirect_stderr=true
stdout_logfile=/var/log/docreader.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PYTHONPATH="/app/src" 

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock 
