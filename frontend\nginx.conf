server {
    listen 80;
    server_name localhost;
    client_max_body_size 50M;

    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # API请求代理到后端服务
    location /api/ {
        proxy_pass http://app:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE 相关配置
        proxy_http_version 1.1;                    # 使用 HTTP/1.1
        proxy_set_header Connection "";            # 禁用 Connection: close，保持连接打开
        chunked_transfer_encoding off;             # 关闭分块传输编码
        proxy_buffering off;                       # 关闭缓冲
        proxy_cache off;                           # 关闭缓存
        proxy_read_timeout 3600s;                  # 增加读取超时时间
        proxy_send_timeout 3600s;                  # 增加发送超时时间
    }

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}