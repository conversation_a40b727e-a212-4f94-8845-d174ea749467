version: "3.8"

services:
  app:
    build:
      context: .
      dockerfile: docker/Dockerfile.app
    container_name: WeKnora-app
    ports:
      - "8080:8080"
    volumes:
      - data-files:/data/files
      - ./config:/app/config
    environment:
      - GIN_MODE=${GIN_MODE}
      - DB_DRIVER=${DB_DRIVER}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - TZ=Asia/Shanghai
      - OTEL_EXPORTER_OTLP_ENDPOINT=jaeger:4317
      - OTEL_SERVICE_NAME=WeKnora
      - OTEL_TRACES_EXPORTER=otlp
      - OTEL_METRICS_EXPORTER=none
      - OTEL_LOGS_EXPORTER=none
      - OTEL_PROPAGATORS=tracecontext,baggage
      - RETRIEVE_DRIVER=${RETRIEVE_DRIVER}
      - ELASTICSEARCH_ADDR=${ELA<PERSON><PERSON><PERSON><PERSON><PERSON>_ADDR}
      - ELASTICSEARCH_USERNAME=${<PERSON>LASTICSEARCH_USERNAME}
      - ELASTICSEARCH_PASSWORD=${ELASTICSEARCH_PASSWORD}
      - ELASTICSEARCH_INDEX=${ELASTICSEARCH_INDEX}
      - DOCREADER_ADDR=docreader:50051
      - STORAGE_TYPE=${STORAGE_TYPE}
      - LOCAL_STORAGE_BASE_DIR=${LOCAL_STORAGE_BASE_DIR}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_ACCESS_KEY_ID=${MINIO_ACCESS_KEY_ID}
      - MINIO_SECRET_ACCESS_KEY=${MINIO_SECRET_ACCESS_KEY}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL}
      - STREAM_MANAGER_TYPE=${STREAM_MANAGER_TYPE}
      - REDIS_ADDR=${REDIS_HOST}:${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=${REDIS_DB}
      - REDIS_PREFIX=${REDIS_PREFIX}
      - ENABLE_GRAPH_RAG=${ENABLE_GRAPH_RAG}
      - TENANT_AES_KEY=${TENANT_AES_KEY}
      - INIT_LLM_MODEL_NAME=${INIT_LLM_MODEL_NAME}
      - INIT_LLM_MODEL_BASE_URL=${INIT_LLM_MODEL_BASE_URL}
      - INIT_LLM_MODEL_API_KEY=${INIT_LLM_MODEL_API_KEY}
      - INIT_EMBEDDING_MODEL_NAME=${INIT_EMBEDDING_MODEL_NAME}
      - INIT_EMBEDDING_MODEL_BASE_URL=${INIT_EMBEDDING_MODEL_BASE_URL}
      - INIT_EMBEDDING_MODEL_API_KEY=${INIT_EMBEDDING_MODEL_API_KEY}
      - INIT_EMBEDDING_MODEL_DIMENSION=${INIT_EMBEDDING_MODEL_DIMENSION}
      - INIT_EMBEDDING_MODEL_ID=${INIT_EMBEDDING_MODEL_ID}
      - INIT_RERANK_MODEL_NAME=${INIT_RERANK_MODEL_NAME}
      - INIT_RERANK_MODEL_BASE_URL=${INIT_RERANK_MODEL_BASE_URL}
      - INIT_RERANK_MODEL_API_KEY=${INIT_RERANK_MODEL_API_KEY}
      - INIT_TEST_TENANT_ID=${INIT_TEST_TENANT_ID}
      - INIT_TEST_KNOWLEDGE_BASE_ID=${INIT_TEST_KNOWLEDGE_BASE_ID}
    depends_on:
      redis:
        condition: service_started
      postgres:
        condition: service_healthy
    networks:
      - WeKnora-network
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: WeKnora-frontend
    ports:
      - "80:80"
    depends_on:
      - app
    networks:
      - WeKnora-network
    restart: unless-stopped

  docreader:
    build:
      context: .
      dockerfile: docker/Dockerfile.docreader
    container_name: WeKnora-docreader
    ports:
      - "50051:50051"
    environment:
      - COS_SECRET_ID=${COS_SECRET_ID}
      - COS_SECRET_KEY=${COS_SECRET_KEY}
      - COS_REGION=${COS_REGION}
      - COS_BUCKET_NAME=${COS_BUCKET_NAME}
      - COS_APP_ID=${COS_APP_ID}
      - COS_PATH_PREFIX=${COS_PATH_PREFIX}
      - COS_ENABLE_OLD_DOMAIN=${COS_ENABLE_OLD_DOMAIN}
      - VLM_MODEL_BASE_URL=${VLM_MODEL_BASE_URL}
      - VLM_MODEL_NAME=${VLM_MODEL_NAME}
    networks:
      - WeKnora-network
    restart: unless-stopped

  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "6831:6831/udp" # Jaeger Thrift接收器
      - "6832:6832/udp" # Jaeger Thrift接收器(Compact)
      - "5778:5778" # 配置端口
      - "16686:16686" # Web UI
      - "4317:4317" # OTLP gRPC接收器
      - "4318:4318" # OTLP HTTP接收器
      - "14250:14250" # 接收模型端口
      - "14268:14268" # Jaeger HTTP接收器
      - "9411:9411" # Zipkin兼容性端口
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    volumes:
      - jaeger_data:/var/lib/jaeger # 持久化 Jaeger 数据
    networks:
      - WeKnora-network
    restart: unless-stopped
  # 修改的PostgreSQL配置
  postgres:
    image: paradedb/paradedb:latest
    container_name: WeKnora-postgres
    ports:
      - "${DB_PORT}:5432"
    environment:
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_NAME}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./migrations/paradedb:/docker-entrypoint-initdb.d
    networks:
      - WeKnora-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER}"]
      interval: 10s # 增加时间间隔
      timeout: 10s # 增加超时时间
      retries: 3 # 减少重试次数，让失败更快反馈
      start_period: 30s # 给予初始启动更多时间
    restart: unless-stopped
    # 添加停机时的优雅退出时间
    stop_grace_period: 1m

  redis:
    image: redis:7.0-alpine
    container_name: WeKnora-redis
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    restart: always
    networks:
      - WeKnora-network

networks:
  WeKnora-network:
    driver: bridge

volumes:
  postgres-data:
  data-files:
  jaeger_data:
  redis_data:
