<PERSON><PERSON> is pleased to support the open source community by making this project available.

Copyright (C) 2025 Tencent. All rights reserved. 

This project is licensed under the MIT License except for the third-party components listed below, which is licensed under different terms. <PERSON><PERSON> does not impose any additional limitations beyond what is outlined in the respective licenses of these third-party components. Users must comply with all terms and conditions of original licenses of these third-party components and must ensure that the usage of the third party components adheres to all relevant laws and regulations. 


Terms of the MIT License:
--------------------------------------------------------------------
Copyright (C) 2025 Tencent. All rights reserved. 

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the " Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice (including the next paragraph) shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

--------------------------------------------------------------------
Other third-party components:

Open Source Software Licensed under the Apache-2.0: 
-------------------------------------------------------------------- 
1. otel-1.37.0
Copyright (c) 2025 opentelemetry-go original author and authors

2. stdr-1.2.2
Copyright (c) 2021 stdr original author and authors

3. parquet-go-0.25.0
Copyright 2023 Twilio, Inc.

4. rpc-0.0.0-20250603155806-513f23925822
Copyright (c) 2025 go-genproto original author and authors

5. api-0.0.0-20250603155806-513f23925822
Copyright (c) 2025 go-genproto original author and authors

6. grpc-1.73.0
Copyright (c) 2025 grpc-go original author and authors

7. base64x-0.1.5
Copyright (c) 2025 base64x original author and authors

8. trace-1.37.0
Copyright (c) 2025 opentelemetry-go original author and authors

9. loader-0.2.4
Copyright 2025 ByteDance Inc.

10. paddlepaddle-gpu-3.0.0
Copyright (c) 2016 PaddlePaddle Authors. All Rights Reserved

11. otlp-1.7.0
Copyright (c) 2025 opentelemetry-proto original author and authors

12. logr-1.4.3
Copyright (c) 2025 logr original author and authors

13. otlptrace-1.37.0
Copyright (c) 2025 opentelemetry-go original author and authors

14. metric-1.37.0
Copyright (c) 2025 opentelemetry-go original author and authors

15. sonic-1.13.2
Copyright (c) 2025 sonic original author and authors

16. go-openai-1.40.5
Copyright (c) 2025 go-openai original author and authors

17. crc64nvme-1.0.1
Copyright (c) 2025 crc64nvme original author and authors

18. concurrent-0.0.0-20180306012644-bacd9c7ef1dd
Copyright (c) 2018 concurrent original author and authors

19. stdouttrace-1.35.0
Copyright (c) 2025 opentelemetry-go original author and authors

20. otlptracegrpc-1.37.0
Copyright (c) 2025 opentelemetry-go original author and authors

21. paddleocr-3.0.0
Copyright (c) 2016 PaddlePaddle Authors. All Rights Reserved.

 

Terms of the Apache-2.0: 
Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License. 

Open Source Software Licensed under the BSD-2-Clause: 
-------------------------------------------------------------------- 
1. esrecurse-4.3.0
Copyright (C) 2014 [Yusuke Suzuki](https://github.com/Constellation) (twitter: [@Constellation](https://twitter.com/Constellation)) and other contributors.

2. glob-to-regexp-0.4.1
Copyright (c) 2013, Nick Fitzgerald

3. eslint-scope-5.1.1
Copyright(c) 2025 eslint-scope original author and authors

 

Terms of the BSD-2-Clause: 
Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR
ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. 

Open Source Software Licensed under the BSD-3-Clause: 
-------------------------------------------------------------------- 
1. text-0.26.0
Copyright 2009 The Go Authors.

2. compress-1.18.0
Copyright (c) 2012 The Go Authors. All rights reserved.
Copyright (c) 2019 Klaus Post. All rights reserved.
Copyright 2016-2017 The New York Times Company
Copyright (c) 2015 Klaus Post
Copyright 2016 The filepath Authors

3. net-0.41.0
Copyright 2009 The Go Authors.

4. fsnotify-1.8.0
Copyright © 2012 The Go Authors. All rights reserved.
Copyright © fsnotify Authors. All rights reserved.

5. sys-0.33.0
Copyright 2009 The Go Authors.

6. protobuf-1.36.6
Copyright (c) 2018 The Go Authors. All rights reserved.

7. arch-0.15.0
Copyright 2015 The Go Authors.

 

Terms of the BSD-3-Clause: 
Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

Neither the name of the ORGANIZATION nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. 

Open Source Software Licensed under the CC-BY-4.0: 
-------------------------------------------------------------------- 
1. caniuse-lite-1.0.30001727
Copyright(c) 2025 Ben Briggs

 

Terms of the CC-BY-4.0: 
Attribution 4.0 International

=======================================================================

Creative Commons Corporation ("Creative Commons") is not a law firm and
does not provide legal services or legal advice. Distribution of
Creative Commons public licenses does not create a lawyer-client or
other relationship. Creative Commons makes its licenses and related
information available on an "as-is" basis. Creative Commons gives no
warranties regarding its licenses, any material licensed under their
terms and conditions, or any related information. Creative Commons
disclaims all liability for damages resulting from their use to the
fullest extent possible.

Using Creative Commons Public Licenses

Creative Commons public licenses provide a standard set of terms and
conditions that creators and other rights holders may use to share
original works of authorship and other material subject to copyright
and certain other rights specified in the public license below. The
following considerations are for informational purposes only, are not
exhaustive, and do not form part of our licenses.

     Considerations for licensors: Our public licenses are
     intended for use by those authorized to give the public
     permission to use material in ways otherwise restricted by
     copyright and certain other rights. Our licenses are
     irrevocable. Licensors should read and understand the terms
     and conditions of the license they choose before applying it.
     Licensors should also secure all rights necessary before
     applying our licenses so that the public can reuse the
     material as expected. Licensors should clearly mark any
     material not subject to the license. This includes other CC-
     licensed material, or material used under an exception or
     limitation to copyright. More considerations for licensors:
	wiki.creativecommons.org/Considerations_for_licensors

     Considerations for the public: By using one of our public
     licenses, a licensor grants the public permission to use the
     licensed material under specified terms and conditions. If
     the licensor's permission is not necessary for any reason--for
     example, because of any applicable exception or limitation to
     copyright--then that use is not regulated by the license. Our
     licenses grant only permissions under copyright and certain
     other rights that a licensor has authority to grant. Use of
     the licensed material may still be restricted for other
     reasons, including because others have copyright or other
     rights in the material. A licensor may make special requests,
     such as asking that all changes be marked or described.
     Although not required by our licenses, you are encouraged to
     respect those requests where reasonable. More considerations
     for the public: 
	wiki.creativecommons.org/Considerations_for_licensees

=======================================================================

Creative Commons Attribution 4.0 International Public License

By exercising the Licensed Rights (defined below), You accept and agree
to be bound by the terms and conditions of this Creative Commons
Attribution 4.0 International Public License ("Public License"). To the
extent this Public License may be interpreted as a contract, You are
granted the Licensed Rights in consideration of Your acceptance of
these terms and conditions, and the Licensor grants You such rights in
consideration of benefits the Licensor receives from making the
Licensed Material available under these terms and conditions.


Section 1 -- Definitions.

  a. Adapted Material means material subject to Copyright and Similar
     Rights that is derived from or based upon the Licensed Material
     and in which the Licensed Material is translated, altered,
     arranged, transformed, or otherwise modified in a manner requiring
     permission under the Copyright and Similar Rights held by the
     Licensor. For purposes of this Public License, where the Licensed
     Material is a musical work, performance, or sound recording,
     Adapted Material is always produced where the Licensed Material is
     synched in timed relation with a moving image.

  b. Adapter's License means the license You apply to Your Copyright
     and Similar Rights in Your contributions to Adapted Material in
     accordance with the terms and conditions of this Public License.

  c. Copyright and Similar Rights means copyright and/or similar rights
     closely related to copyright including, without limitation,
     performance, broadcast, sound recording, and Sui Generis Database
     Rights, without regard to how the rights are labeled or
     categorized. For purposes of this Public License, the rights
     specified in Section 2(b)(1)-(2) are not Copyright and Similar
     Rights.

  d. Effective Technological Measures means those measures that, in the
     absence of proper authority, may not be circumvented under laws
     fulfilling obligations under Article 11 of the WIPO Copyright
     Treaty adopted on December 20, 1996, and/or similar international
     agreements.

  e. Exceptions and Limitations means fair use, fair dealing, and/or
     any other exception or limitation to Copyright and Similar Rights
     that applies to Your use of the Licensed Material.

  f. Licensed Material means the artistic or literary work, database,
     or other material to which the Licensor applied this Public
     License.

  g. Licensed Rights means the rights granted to You subject to the
     terms and conditions of this Public License, which are limited to
     all Copyright and Similar Rights that apply to Your use of the
     Licensed Material and that the Licensor has authority to license.

  h. Licensor means the individual(s) or entity(ies) granting rights
     under this Public License.

  i. Share means to provide material to the public by any means or
     process that requires permission under the Licensed Rights, such
     as reproduction, public display, public performance, distribution,
     dissemination, communication, or importation, and to make material
     available to the public including in ways that members of the
     public may access the material from a place and at a time
     individually chosen by them.

  j. Sui Generis Database Rights means rights other than copyright
     resulting from Directive 96/9/EC of the European Parliament and of
     the Council of 11 March 1996 on the legal protection of databases,
     as amended and/or succeeded, as well as other essentially
     equivalent rights anywhere in the world.

  k. You means the individual or entity exercising the Licensed Rights
     under this Public License. Your has a corresponding meaning.


Section 2 -- Scope.

  a. License grant.

       1. Subject to the terms and conditions of this Public License,
          the Licensor hereby grants You a worldwide, royalty-free,
          non-sublicensable, non-exclusive, irrevocable license to
          exercise the Licensed Rights in the Licensed Material to:

            a. reproduce and Share the Licensed Material, in whole or
               in part; and

            b. produce, reproduce, and Share Adapted Material.

       2. Exceptions and Limitations. For the avoidance of doubt, where
          Exceptions and Limitations apply to Your use, this Public
          License does not apply, and You do not need to comply with
          its terms and conditions.

       3. Term. The term of this Public License is specified in Section
          6(a).

       4. Media and formats; technical modifications allowed. The
          Licensor authorizes You to exercise the Licensed Rights in
          all media and formats whether now known or hereafter created,
          and to make technical modifications necessary to do so. The
          Licensor waives and/or agrees not to assert any right or
          authority to forbid You from making technical modifications
          necessary to exercise the Licensed Rights, including
          technical modifications necessary to circumvent Effective
          Technological Measures. For purposes of this Public License,
          simply making modifications authorized by this Section 2(a)
          (4) never produces Adapted Material.

       5. Downstream recipients.

            a. Offer from the Licensor -- Licensed Material. Every
               recipient of the Licensed Material automatically
               receives an offer from the Licensor to exercise the
               Licensed Rights under the terms and conditions of this
               Public License.

            b. No downstream restrictions. You may not offer or impose
               any additional or different terms or conditions on, or
               apply any Effective Technological Measures to, the
               Licensed Material if doing so restricts exercise of the
               Licensed Rights by any recipient of the Licensed
               Material.

       6. No endorsement. Nothing in this Public License constitutes or
          may be construed as permission to assert or imply that You
          are, or that Your use of the Licensed Material is, connected
          with, or sponsored, endorsed, or granted official status by,
          the Licensor or others designated to receive attribution as
          provided in Section 3(a)(1)(A)(i).

  b. Other rights.

       1. Moral rights, such as the right of integrity, are not
          licensed under this Public License, nor are publicity,
          privacy, and/or other similar personality rights; however, to
          the extent possible, the Licensor waives and/or agrees not to
          assert any such rights held by the Licensor to the limited
          extent necessary to allow You to exercise the Licensed
          Rights, but not otherwise.

       2. Patent and trademark rights are not licensed under this
          Public License.

       3. To the extent possible, the Licensor waives any right to
          collect royalties from You for the exercise of the Licensed
          Rights, whether directly or through a collecting society
          under any voluntary or waivable statutory or compulsory
          licensing scheme. In all other cases the Licensor expressly
          reserves any right to collect such royalties.


Section 3 -- License Conditions.

Your exercise of the Licensed Rights is expressly made subject to the
following conditions.

  a. Attribution.

       1. If You Share the Licensed Material (including in modified
          form), You must:

            a. retain the following if it is supplied by the Licensor
               with the Licensed Material:

                 i. identification of the creator(s) of the Licensed
                    Material and any others designated to receive
                    attribution, in any reasonable manner requested by
                    the Licensor (including by pseudonym if
                    designated);

                ii. a copyright notice;

               iii. a notice that refers to this Public License;

                iv. a notice that refers to the disclaimer of
                    warranties;

                 v. a URI or hyperlink to the Licensed Material to the
                    extent reasonably practicable;

            b. indicate if You modified the Licensed Material and
               retain an indication of any previous modifications; and

            c. indicate the Licensed Material is licensed under this
               Public License, and include the text of, or the URI or
               hyperlink to, this Public License.

       2. You may satisfy the conditions in Section 3(a)(1) in any
          reasonable manner based on the medium, means, and context in
          which You Share the Licensed Material. For example, it may be
          reasonable to satisfy the conditions by providing a URI or
          hyperlink to a resource that includes the required
          information.

       3. If requested by the Licensor, You must remove any of the
          information required by Section 3(a)(1)(A) to the extent
          reasonably practicable.

       4. If You Share Adapted Material You produce, the Adapter's
          License You apply must not prevent recipients of the Adapted
          Material from complying with this Public License.


Section 4 -- Sui Generis Database Rights.

Where the Licensed Rights include Sui Generis Database Rights that
apply to Your use of the Licensed Material:

  a. for the avoidance of doubt, Section 2(a)(1) grants You the right
     to extract, reuse, reproduce, and Share all or a substantial
     portion of the contents of the database;

  b. if You include all or a substantial portion of the database
     contents in a database in which You have Sui Generis Database
     Rights, then the database in which You have Sui Generis Database
     Rights (but not its individual contents) is Adapted Material; and

  c. You must comply with the conditions in Section 3(a) if You Share
     all or a substantial portion of the contents of the database.

For the avoidance of doubt, this Section 4 supplements and does not
replace Your obligations under this Public License where the Licensed
Rights include other Copyright and Similar Rights.


Section 5 -- Disclaimer of Warranties and Limitation of Liability.

  a. UNLESS OTHERWISE SEPARATELY UNDERTAKEN BY THE LICENSOR, TO THE
     EXTENT POSSIBLE, THE LICENSOR OFFERS THE LICENSED MATERIAL AS-IS
     AND AS-AVAILABLE, AND MAKES NO REPRESENTATIONS OR WARRANTIES OF
     ANY KIND CONCERNING THE LICENSED MATERIAL, WHETHER EXPRESS,
     IMPLIED, STATUTORY, OR OTHER. THIS INCLUDES, WITHOUT LIMITATION,
     WARRANTIES OF TITLE, MERCHANTABILITY, FITNESS FOR A PARTICULAR
     PURPOSE, NON-INFRINGEMENT, ABSENCE OF LATENT OR OTHER DEFECTS,
     ACCURACY, OR THE PRESENCE OR ABSENCE OF ERRORS, WHETHER OR NOT
     KNOWN OR DISCOVERABLE. WHERE DISCLAIMERS OF WARRANTIES ARE NOT
     ALLOWED IN FULL OR IN PART, THIS DISCLAIMER MAY NOT APPLY TO YOU.

  b. TO THE EXTENT POSSIBLE, IN NO EVENT WILL THE LICENSOR BE LIABLE
     TO YOU ON ANY LEGAL THEORY (INCLUDING, WITHOUT LIMITATION,
     NEGLIGENCE) OR OTHERWISE FOR ANY DIRECT, SPECIAL, INDIRECT,
     INCIDENTAL, CONSEQUENTIAL, PUNITIVE, EXEMPLARY, OR OTHER LOSSES,
     COSTS, EXPENSES, OR DAMAGES ARISING OUT OF THIS PUBLIC LICENSE OR
     USE OF THE LICENSED MATERIAL, EVEN IF THE LICENSOR HAS BEEN
     ADVISED OF THE POSSIBILITY OF SUCH LOSSES, COSTS, EXPENSES, OR
     DAMAGES. WHERE A LIMITATION OF LIABILITY IS NOT ALLOWED IN FULL OR
     IN PART, THIS LIMITATION MAY NOT APPLY TO YOU.

  c. The disclaimer of warranties and limitation of liability provided
     above shall be interpreted in a manner that, to the extent
     possible, most closely approximates an absolute disclaimer and
     waiver of all liability.


Section 6 -- Term and Termination.

  a. This Public License applies for the term of the Copyright and
     Similar Rights licensed here. However, if You fail to comply with
     this Public License, then Your rights under this Public License
     terminate automatically.

  b. Where Your right to use the Licensed Material has terminated under
     Section 6(a), it reinstates:

       1. automatically as of the date the violation is cured, provided
          it is cured within 30 days of Your discovery of the
          violation; or

       2. upon express reinstatement by the Licensor.

     For the avoidance of doubt, this Section 6(b) does not affect any
     right the Licensor may have to seek remedies for Your violations
     of this Public License.

  c. For the avoidance of doubt, the Licensor may also offer the
     Licensed Material under separate terms or conditions or stop
     distributing the Licensed Material at any time; however, doing so
     will not terminate this Public License.

  d. Sections 1, 5, 6, 7, and 8 survive termination of this Public
     License.


Section 7 -- Other Terms and Conditions.

  a. The Licensor shall not be bound by any additional or different
     terms or conditions communicated by You unless expressly agreed.

  b. Any arrangements, understandings, or agreements regarding the
     Licensed Material not stated herein are separate from and
     independent of the terms and conditions of this Public License.


Section 8 -- Interpretation.

  a. For the avoidance of doubt, this Public License does not, and
     shall not be interpreted to, reduce, limit, restrict, or impose
     conditions on any use of the Licensed Material that could lawfully
     be made without permission under this Public License.

  b. To the extent possible, if any provision of this Public License is
     deemed unenforceable, it shall be automatically reformed to the
     minimum extent necessary to make it enforceable. If the provision
     cannot be reformed, it shall be severed from this Public License
     without affecting the enforceability of the remaining terms and
     conditions.

  c. No term or condition of this Public License will be waived and no
     failure to comply consented to unless expressly agreed to by the
     Licensor.

  d. Nothing in this Public License constitutes or may be interpreted
     as a limitation upon, or waiver of, any privileges and immunities
     that apply to the Licensor or You, including from the legal
     processes of any jurisdiction or authority.


=======================================================================

Creative Commons is not a party to its public
licenses. Notwithstanding, Creative Commons may elect to apply one of
its public licenses to material it publishes and in those instances
will be considered the “Licensor.” The text of the Creative Commons
public licenses is dedicated to the public domain under the CC0 Public
Domain Dedication. Except for the limited purpose of indicating that
material is shared under a Creative Commons public license or as
otherwise permitted by the Creative Commons policies published at
creativecommons.org/policies, Creative Commons does not authorize the
use of the trademark "Creative Commons" or any other trademark or logo
of Creative Commons without its prior written consent including,
without limitation, in connection with any unauthorized modifications
to any of its public licenses or any other arrangements,
understandings, or agreements concerning use of licensed material. For
the avoidance of doubt, this paragraph does not form part of the
public licenses.

Creative Commons may be contacted at creativecommons.org.
 

Open Source Software Licensed under the ISC: 
-------------------------------------------------------------------- 
1. electron-to-chromium-1.5.183
Copyright 2018 Kilian Valkhof

 

Terms of the ISC: 
Permission to use, copy, modify, and/or distribute this software for any purpose
with or without fee is hereby granted, provided that the above copyright notice
and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS
OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF
THIS SOFTWARE.
 

Open Source Software Licensed under the MIT: 
-------------------------------------------------------------------- 
1. birpc-2.5.0
Copyright (c) 2021 Anthony Fu <https://github.com/antfu>

2. @babel/generator-7.28.0
Copyright (c) 2014-present Sebastian Mckenzie and other contributors

3. neo-async-2.6.2
Copyright (c) 2014-2018 Suguru Motegi

4. parse-node-version-1.0.1
Copyright (c) 2018 Blaine Bublitz <<EMAIL>> and Eric Schoffstall <<EMAIL>>

5. cors-1.7.5
Copyright (c) 2016 Gin-Gonic

6. vue-3.5.17
Copyright (c) 2018-present, Yuxi (Evan) You and Vue contributors

7. copy-anything-2.0.6
Copyright (c) 2018 Luca Ban - Mesqueeb

8. yaml.v3-3.0.1
Copyright (c) 2006-2010 Kirill Simonov
Copyright (c) 2006-2011 Kirill Simonov
Copyright (c) 2011-2019 Canonical Ltd

9. gojieba-1.4.5
Copyright (c) The project creators and maintainers

10. codec-1.2.12
Copyright (c) 2012-2020 Ugorji Nwoke.

11. vue-tsc-2.2.12
Copyright(c) 2025 vue-tsc original author and authors

12. follow-redirects-1.15.9
Copyright 2017-present Oliviver Lalonde <<EMAIL>>, James Talmage <<EMAIL>>, Ruben Verborgh

13. @vue/server-renderer-3.5.17
Copyright (c) 2018-present, Yuxi (Evan) You and Vue contributors

14. pgvector-go-0.3.0
Copyright (c) 2021-2025 Andrew Kane

15. @babel/traverse-7.28.0
Copyright (c) 2014-present Sebastian McKenzie and other contributors

16. @webassemblyjs/helper-api-error-1.13.2
Copyright (c) 2018 Sven Sauleau <<EMAIL>>

17. estree-walker-2.0.2
Copyright (c) 2015-20 [these people](https://github.com/Rich-Harris/estree-walker/graphs/contributors)

18. @types/node-22.16.3
Copyright(c) 2025 @types/node original author and authors

19. magic-string-0.30.17
Copyright 2018 Rich Harris

20. pinia-3.0.3
Copyright (c) 2019-present Eduardo San Martin Morote

21. @types/lodash-4.17.20
Copyright(c) 2025 @types/lodash original author and authors

22. es-set-tostringtag-2.1.0
Copyright (c) 2022 ECMAScript Shims

23. @vue/runtime-dom-3.5.17
Copyright (c) 2018-present, Yuxi (Evan) You and Vue contributors

24. @vue/reactivity-3.5.17
Copyright (c) 2018-present, Yuxi (Evan) You and Vue contributors

25. de-indent-1.0.2
Copyright(c) 2025 Evan You

26. @babel/template-7.27.2
Copyright (c) 2014-present Sebastian McKenzie and other contributors

27. tdesign-icons-vue-next-0.3.6
Copyright (c) 2021-present TDesign

28. webpack-5.100.1
Copyright JS Foundation and other contributors

29. asynq-0.25.1
Copyright (c) 2019 Kentaro Hibino

30. shell-quote-1.8.3
Copyright (c) 2013 James Halliday (<EMAIL>)

31. acorn-8.15.0
Copyright(c) 2025 acorn original author and authors

32. esbuild-0.25.6
Copyright (c) 2020 Evan Wallace

33. tdesign-vue-next-1.14.2
Copyright (c) 2021-present TDesign

34. @vue/runtime-core-3.5.17
Copyright (c) 2018-present, Yuxi (Evan) You and Vue contributors

35. validator-13.15.15
************************************************************************

36. postcss-8.5.6
Copyright 2013 Andrey Sitnik <<EMAIL>>

37. mimetype-1.4.8
Copyright (c) 2018 Gabriel Vasile

38. @babel/helper-globals-7.28.0
Copyright (c) 2014-present Sebastian McKenzie and other contributors

39. @babel/helper-create-class-features-plugin-7.27.1
Copyright (c) 2014-present Sebastian McKenzieg and other contributors

40. @babel/helper-replace-supers-7.27.1
Copyright (c) 2014-present Sebastian McKenzie and other contributors

41. is-what-3.14.1
Copyright (c) 2018 Luca Ban - Mesqueeb

42. @rolldown/pluginutils-1.0.0-beta.27
Copyright (c) 2024-present VoidZero Inc. & Contributors

43. ollama-0.9.6
Copyright (c) Ollama

44. locafero-0.7.0
Copyright (c) 2023 Márk Sági-Kazar <<EMAIL>>

45. stylus->=0.54.8
Copyright (c) Automatttic <developer.wordpress.com>

46. @vue/language-core-2.2.12
Copyright (c) 2021-present Johnson Chu

47. @babel/parser-7.28.0
Copyright (c) 2014-present Sebastian MacKenzie and other contributors

48. cos-go-sdk-v5-0.7.65
Copyright (c) 2017 mozillazg

49. @rolldown/pluginutils-1.0.0-beta.19
Copyright (c) 2024-present VoidZero Inc. & Contributors

50. @vue/compiler-core-3.5.17
Copyright (c) 2018-present, Yuxi (Evan) You and Vue contributors

51. @vue/shared-3.5.17
Copyright (c) 2018-present, Yuxi (Evan) You and Vue contributors

52. @vitejs/plugin-vue-jsx-5.0.1
Copyright (c) 2019-present, Yuxi
(Evan) You and Vite contributors

53. fdir-6.4.6
Copyright 2023 AbdullAh AttA

54. go-rendezvous-0.0.0-20200823014737-9f7001d12a5f
Copyright (c) 2017-2020 Damian Gryski <<EMAIL>>

55. @babel/plugin-syntax-jsx-7.27.1
Copyright (c) 2014-present Sebastian McKenzie and other contributors

56. @vue/compiler-ssr-3.5.17
Copyright (c) 2018-present, Yuxi (Evan) You and Vue contributors

57. @babel/helper-member-expression-to-functions-7.27.1
Copyright (c) 2014-present Sebastian McKenzie and other contributors

58. es-module-lexer-1.7.0
Copyright (C) 2018-2022 Guy Bedford

59. vue-router-4.5.1
Copyright (c) 2019-present Eduardo San Martin Morote

60. @babel/helper-validator-option-7.27.1
Copyright (c) 2014-present Sebastian McKenzie and other contributors

61. @vue/compiler-sfc-3.5.17
Copyright (c) 2018-present, Yuxi (Evan) You and Vue contributors

62. @vue/devtools-shared-7.7.7
Copyright (c) 2023 webfansplz

63. gensync-1.0.0-beta.2
Copyright 2018 Logan Smyth <<EMAIL>>

64. @rspack/core-0.x || 1.x
Copyright (c) 2022-present Bytsedance Inc. and its affiliates.

65. jiti->=1.21.0
Copyright (c) Pooya Parsa <<EMAIL>>

66. pgservicefile-0.0.0-20240606120523-5a60cdf6a761
Copyright (c) 2020 Jack Christensens

67. @types/validator-13.15.2
Copyright(c) 2025 @types/validator original author and authors

68. @vue/devtools-api-7.7.7
Copyright (c) 2023 webfansplz

69. tapable-2.2.2
Copyright JS Foundation and
other contributors

70. @vitejs/plugin-vue-6.0.0
Copyright (c) 2019-present, Yuxhi (Evan) You and Vite contributors

71. @babel/core-7.28.0
Copyright (c) 2014-present Sebastian McInerney and other contributors

72. @babel/types-7.28.1
Copyright (c) 2014-present Sebastian McPkenzie and other contributors

73. @babel/compat-data-7.28.0
Copyright (c) 2014-present Sebastian MacKenzie and other contributors

74. @volar/typescript-2.4.15
Copyright (c) 2021-present Johnson Chu

75. @babel/helper-annotate-as-pure-7.27.3
Copyright (c) 2014-present Sebastian McKenzie and other contributors

76. vite-7.0.4
Copyright (c) 2019-present, VoidZero Inc. and Vite contributors

77. @volar/source-map-2.4.15
Copyright (c) 2021-present Johnson Chu

78. json-parse-even-better-errors-2.3.1
Copyright 2017 Kat Marchán
Copyright npm, Inc.

79. @vue/compiler-dom-3.5.17
Copyright (c) 2018-present, Yuxia (Evan) You and Vue contributors

80. @volar/language-core-2.4.15
Copyright (c) 2021-present Johnson Chu

81. @vue/devtools-kit-7.7.7
Copyright (c) 2023 webfansplz

82. @babel/helper-skip-transparent-expression-wrappers-7.27.1
Copyright (c) 2014-present Sebastian MacKenzie and other contributors

83. mitt-3.0.1
Copyright (c) 2021 Jason Miller

84. acorn-import-phases-1.0.4
Copyright (c) 2025 Nicolò Ribaudo

85. @babel/helper-string-parser-7.27.1
Copyright (c) 2014-present Sebastian McKeenzied and other contributors

86. watchpack-2.4.4
Copyright JS Foundation and other contributors

87. @babel/runtime-7.27.6
Copyright (c) 2014-present Sebastian McKenzie and other contributors

88. @babel/plugin-transform-typescript-7.28.0
Copyright (c) 2014-present Sebastian McKenzie and other contributors

89. jest-worker-27.5.1
Copyright (c) jest-worker authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/jest-worker.

 

Terms of the MIT: 
Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE. 

Open Source Software Licensed under the MIT AND ISC AND 0BSD: 
-------------------------------------------------------------------- 
1. rollup-4.45.0
Copyright(c) 2025 Rich Harris

 

Terms of the MIT AND ISC AND 0BSD: 
Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE. 

Open Source Software Licensed under the MIT-CMU: 
-------------------------------------------------------------------- 
1. Pillow-default
Copyright © 1997-2011 by Secret Labs AB
Copyright © 1995-2011 by Fredrik Lundh and contributors
Copyright © 2010 by Jeffrey A. Clark and contributors

 

Terms of the MIT-CMU: 
Permission to use, copy, modify and distribute this software and its
documentation for any purpose and without fee is hereby granted, provided that
the above copyright notice appears in all copies and that both that copyright
notice and this permission notice appear in supporting documentation, and that
the name of CMU and The Regents of the University of California not be used in
advertising or publicity pertaining to distribution of the software without
specific written permission.

CMU AND THE REGENTS OF THE UNIVERSITY OF CALIFORNIA DISCLAIM ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS. IN NO EVENT SHALL CMU OR THE REGENTS OF THE UNIVERSITY OF CALIFORNIA BE
LIABLE FOR ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM THE LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 

Open Source Software Licensed under the MPL-2.0: 
-------------------------------------------------------------------- 
1. lightningcss-1.21.0
Copyright(c) 2025 lightningcss original author and authors

 

Terms of the MPL-2.0: 
Mozilla Public License Version 2.0
==================================

1. Definitions
--------------

1.1. "Contributor"
    means each individual or legal entity that creates, contributes to
    the creation of, or owns Covered Software.

1.2. "Contributor Version"
    means the combination of the Contributions of others (if any) used
    by a Contributor and that particular Contributor's Contribution.

1.3. "Contribution"
    means Covered Software of a particular Contributor.

1.4. "Covered Software"
    means Source Code Form to which the initial Contributor has attached
    the notice in Exhibit A, the Executable Form of such Source Code
    Form, and Modifications of such Source Code Form, in each case
    including portions thereof.

1.5. "Incompatible With Secondary Licenses"
    means

    (a) that the initial Contributor has attached the notice described
        in Exhibit B to the Covered Software; or

    (b) that the Covered Software was made available under the terms of
        version 1.1 or earlier of the License, but not also under the
        terms of a Secondary License.

1.6. "Executable Form"
    means any form of the work other than Source Code Form.

1.7. "Larger Work"
    means a work that combines Covered Software with other material, in 
    a separate file or files, that is not Covered Software.

1.8. "License"
    means this document.

1.9. "Licensable"
    means having the right to grant, to the maximum extent possible,
    whether at the time of the initial grant or subsequently, any and
    all of the rights conveyed by this License.

1.10. "Modifications"
    means any of the following:

    (a) any file in Source Code Form that results from an addition to,
        deletion from, or modification of the contents of Covered
        Software; or

    (b) any new file in Source Code Form that contains any Covered
        Software.

1.11. "Patent Claims" of a Contributor
    means any patent claim(s), including without limitation, method,
    process, and apparatus claims, in any patent Licensable by such
    Contributor that would be infringed, but for the grant of the
    License, by the making, using, selling, offering for sale, having
    made, import, or transfer of either its Contributions or its
    Contributor Version.

1.12. "Secondary License"
    means either the GNU General Public License, Version 2.0, the GNU
    Lesser General Public License, Version 2.1, the GNU Affero General
    Public License, Version 3.0, or any later versions of those
    licenses.

1.13. "Source Code Form"
    means the form of the work preferred for making modifications.

1.14. "You" (or "Your")
    means an individual or a legal entity exercising rights under this
    License. For legal entities, "You" includes any entity that
    controls, is controlled by, or is under common control with You. For
    purposes of this definition, "control" means (a) the power, direct
    or indirect, to cause the direction or management of such entity,
    whether by contract or otherwise, or (b) ownership of more than
    fifty percent (50%) of the outstanding shares or beneficial
    ownership of such entity.

2. License Grants and Conditions
--------------------------------

2.1. Grants

Each Contributor hereby grants You a world-wide, royalty-free,
non-exclusive license:

(a) under intellectual property rights (other than patent or trademark)
    Licensable by such Contributor to use, reproduce, make available,
    modify, display, perform, distribute, and otherwise exploit its
    Contributions, either on an unmodified basis, with Modifications, or
    as part of a Larger Work; and

(b) under Patent Claims of such Contributor to make, use, sell, offer
    for sale, have made, import, and otherwise transfer either its
    Contributions or its Contributor Version.

2.2. Effective Date

The licenses granted in Section 2.1 with respect to any Contribution
become effective for each Contribution on the date the Contributor first
distributes such Contribution.

2.3. Limitations on Grant Scope

The licenses granted in this Section 2 are the only rights granted under
this License. No additional rights or licenses will be implied from the
distribution or licensing of Covered Software under this License.
Notwithstanding Section 2.1(b) above, no patent license is granted by a
Contributor:

(a) for any code that a Contributor has removed from Covered Software;
    or

(b) for infringements caused by: (i) Your and any other third party's
    modifications of Covered Software, or (ii) the combination of its
    Contributions with other software (except as part of its Contributor
    Version); or

(c) under Patent Claims infringed by Covered Software in the absence of
    its Contributions.

This License does not grant any rights in the trademarks, service marks,
or logos of any Contributor (except as may be necessary to comply with
the notice requirements in Section 3.4).

2.4. Subsequent Licenses

No Contributor makes additional grants as a result of Your choice to
distribute the Covered Software under a subsequent version of this
License (see Section 10.2) or under the terms of a Secondary License (if
permitted under the terms of Section 3.3).

2.5. Representation

Each Contributor represents that the Contributor believes its
Contributions are its original creation(s) or it has sufficient rights
to grant the rights to its Contributions conveyed by this License.

2.6. Fair Use

This License is not intended to limit any rights You have under
applicable copyright doctrines of fair use, fair dealing, or other
equivalents.

2.7. Conditions

Sections 3.1, 3.2, 3.3, and 3.4 are conditions of the licenses granted
in Section 2.1.

3. Responsibilities
-------------------

3.1. Distribution of Source Form

All distribution of Covered Software in Source Code Form, including any
Modifications that You create or to which You contribute, must be under
the terms of this License. You must inform recipients that the Source
Code Form of the Covered Software is governed by the terms of this
License, and how they can obtain a copy of this License. You may not
attempt to alter or restrict the recipients' rights in the Source Code
Form.

3.2. Distribution of Executable Form

If You distribute Covered Software in Executable Form then:

(a) such Covered Software must also be made available in Source Code
    Form, as described in Section 3.1, and You must inform recipients of
    the Executable Form how they can obtain a copy of such Source Code
    Form by reasonable means in a timely manner, at a charge no more
    than the cost of distribution to the recipient; and

(b) You may distribute such Executable Form under the terms of this
    License, or sublicense it under different terms, provided that the
    license for the Executable Form does not attempt to limit or alter
    the recipients' rights in the Source Code Form under this License.

3.3. Distribution of a Larger Work

You may create and distribute a Larger Work under terms of Your choice,
provided that You also comply with the requirements of this License for
the Covered Software. If the Larger Work is a combination of Covered
Software with a work governed by one or more Secondary Licenses, and the
Covered Software is not Incompatible With Secondary Licenses, this
License permits You to additionally distribute such Covered Software
under the terms of such Secondary License(s), so that the recipient of
the Larger Work may, at their option, further distribute the Covered
Software under the terms of either this License or such Secondary
License(s).

3.4. Notices

You may not remove or alter the substance of any license notices
(including copyright notices, patent notices, disclaimers of warranty,
or limitations of liability) contained within the Source Code Form of
the Covered Software, except that You may alter any license notices to
the extent required to remedy known factual inaccuracies.

3.5. Application of Additional Terms

You may choose to offer, and to charge a fee for, warranty, support,
indemnity or liability obligations to one or more recipients of Covered
Software. However, You may do so only on Your own behalf, and not on
behalf of any Contributor. You must make it absolutely clear that any
such warranty, support, indemnity, or liability obligation is offered by
You alone, and You hereby agree to indemnify every Contributor for any
liability incurred by such Contributor as a result of warranty, support,
indemnity or liability terms You offer. You may include additional
disclaimers of warranty and limitations of liability specific to any
jurisdiction.

4. Inability to Comply Due to Statute or Regulation
---------------------------------------------------

If it is impossible for You to comply with any of the terms of this
License with respect to some or all of the Covered Software due to
statute, judicial order, or regulation then You must: (a) comply with
the terms of this License to the maximum extent possible; and (b)
describe the limitations and the code they affect. Such description must
be placed in a text file included with all distributions of the Covered
Software under this License. Except to the extent prohibited by statute
or regulation, such description must be sufficiently detailed for a
recipient of ordinary skill to be able to understand it.

5. Termination
--------------

5.1. The rights granted under this License will terminate automatically
if You fail to comply with any of its terms. However, if You become
compliant, then the rights granted under this License from a particular
Contributor are reinstated (a) provisionally, unless and until such
Contributor explicitly and finally terminates Your grants, and (b) on an
ongoing basis, if such Contributor fails to notify You of the
non-compliance by some reasonable means prior to 60 days after You have
come back into compliance. Moreover, Your grants from a particular
Contributor are reinstated on an ongoing basis if such Contributor
notifies You of the non-compliance by some reasonable means, this is the
first time You have received notice of non-compliance with this License
from such Contributor, and You become compliant prior to 30 days after
Your receipt of the notice.

5.2. If You initiate litigation against any entity by asserting a patent
infringement claim (excluding declaratory judgment actions,
counter-claims, and cross-claims) alleging that a Contributor Version
directly or indirectly infringes any patent, then the rights granted to
You by any and all Contributors for the Covered Software under Section
2.1 of this License shall terminate.

5.3. In the event of termination under Sections 5.1 or 5.2 above, all
end user license agreements (excluding distributors and resellers) which
have been validly granted by You or Your distributors under this License
prior to termination shall survive termination.

************************************************************************
*                                                                      *
*  6. Disclaimer of Warranty                                           *
*  -------------------------                                           *
*                                                                      *
*  Covered Software is provided under this License on an "as is"       *
*  basis, without warranty of any kind, either expressed, implied, or  *
*  statutory, including, without limitation, warranties that the       *
*  Covered Software is free of defects, merchantable, fit for a        *
*  particular purpose or non-infringing. The entire risk as to the     *
*  quality and performance of the Covered Software is with You.        *
*  Should any Covered Software prove defective in any respect, You     *
*  (not any Contributor) assume the cost of any necessary servicing,   *
*  repair, or correction. This disclaimer of warranty constitutes an   *
*  essential part of this License. No use of any Covered Software is   *
*  authorized under this License except under this disclaimer.         *
*                                                                      *
************************************************************************

************************************************************************
*                                                                      *
*  7. Limitation of Liability                                          *
*  --------------------------                                          *
*                                                                      *
*  Under no circumstances and under no legal theory, whether tort      *
*  (including negligence), contract, or otherwise, shall any           *
*  Contributor, or anyone who distributes Covered Software as          *
*  permitted above, be liable to You for any direct, indirect,         *
*  special, incidental, or consequential damages of any character      *
*  including, without limitation, damages for lost profits, loss of    *
*  goodwill, work stoppage, computer failure or malfunction, or any    *
*  and all other commercial damages or losses, even if such party      *
*  shall have been informed of the possibility of such damages. This   *
*  limitation of liability shall not apply to liability for death or   *
*  personal injury resulting from such party's negligence to the       *
*  extent applicable law prohibits such limitation. Some               *
*  jurisdictions do not allow the exclusion or limitation of           *
*  incidental or consequential damages, so this exclusion and          *
*  limitation may not apply to You.                                    *
*                                                                      *
************************************************************************

8. Litigation
-------------

Any litigation relating to this License may be brought only in the
courts of a jurisdiction where the defendant maintains its principal
place of business and such litigation shall be governed by laws of that
jurisdiction, without reference to its conflict-of-law provisions.
Nothing in this Section shall prevent a party's ability to bring
cross-claims or counter-claims.

9. Miscellaneous
----------------

This License represents the complete agreement concerning the subject
matter hereof. If any provision of this License is held to be
unenforceable, such provision shall be reformed only to the extent
necessary to make it enforceable. Any law or regulation which provides
that the language of a contract shall be construed against the drafter
shall not be used to construe this License against a Contributor.

10. Versions of the License
---------------------------

10.1. New Versions

Mozilla Foundation is the license steward. Except as provided in Section
10.3, no one other than the license steward has the right to modify or
publish new versions of this License. Each version will be given a
distinguishing version number.

10.2. Effect of New Versions

You may distribute the Covered Software under the terms of the version
of the License under which You originally received the Covered Software,
or under the terms of any subsequent version published by the license
steward.

10.3. Modified Versions

If you create software not governed by this License, and you want to
create a new license for such software, you may create and use a
modified version of this License if you rename the license and remove
any references to the name of the license steward (except to note that
such modified license differs from this License).

10.4. Distributing Source Code Form that is Incompatible With Secondary
Licenses

If You choose to distribute Source Code Form that is Incompatible With
Secondary Licenses under the terms of this version of the License, the
notice described in Exhibit B of this License must be attached.

Exhibit A - Source Code Form License Notice
-------------------------------------------

  This Source Code Form is subject to the terms of the Mozilla Public
  License, v. 2.0. If a copy of the MPL was not distributed with this
  file, You can obtain one at http://mozilla.org/MPL/2.0/.

If it is not possible or desirable to put the notice in a particular
file, then You may include the notice in a location (such as a LICENSE
file in a relevant directory) where a recipient would be likely to look
for such a notice.

You may add additional accurate notices of copyright ownership.

Exhibit B - "Incompatible With Secondary Licenses" Notice
---------------------------------------------------------

  This Source Code Form is "Incompatible With Secondary Licenses", as
  defined by the Mozilla Public License, v. 2.0. 

Open Source Software Licensed under the apache-2.0: 
-------------------------------------------------------------------- 
1. @ampproject/remapping-2.3.0
Copyright (c) @ampproject/remapping authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@ampproject/remapping.

2. sync-0.15.0
Copyright (c) sync authors.
You may obtain the source code and detailed information about this component at https://mvnrepository.com/artifact/com.aerospike/sync.

3. @webassemblyjs/leb128-1.13.2
Copyright (c) @webassemblyjs/leb128 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/leb128.

4. sdk-1.37.0
Copyright (c) sdk authors.
You may obtain the source code and detailed information about this component at https://mvnrepository.com/artifact/io.lakefs/sdk.

5. crypto-0.39.0
Copyright (c) crypto authors.
You may obtain the source code and detailed information about this component at https://github.com/smallstep/crypto.

6. grpcio-default
Copyright (c) grpcio authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/grpcio.

7. sdk-1.1.0
Copyright (c) sdk authors.
You may obtain the source code and detailed information about this component at https://mvnrepository.com/artifact/com.jedlix/sdk.

8. afero-1.12.0
Copyright (c) afero authors.
You may obtain the source code and detailed information about this component at https://github.com/spf13/afero.

9. @xtuc/long-4.2.2
Copyright (c) @xtuc/long authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@xtuc/long.

10. chrome-trace-event-1.0.4
Copyright (c) chrome-trace-event authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/chrome-trace-event.

11. reflect2-1.0.2
Copyright (c) reflect2 authors.
You may obtain the source code and detailed information about this component at https://github.com/modern-go/reflect2.

12. grpcio-tools-default
Copyright (c) grpcio-tools authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/grpcio-tools.

13. playwright-default
Copyright (c) playwright authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/playwright.

14. requests-default
Copyright (c) requests authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/requests.

15. ini-1.67.0
Copyright (c) ini authors.
You may obtain the source code and detailed information about this component at https://github.com/go-ini/ini.

16. asyncio-default
Copyright (c) asyncio authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/asyncio.

17. time-0.11.0
Copyright (c) time authors.
You may obtain the source code and detailed information about this component at https://mvnrepository.com/artifact/org.occurrent/time.

18. less-4.3.0
Copyright (c) less authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/less.

19. typescript-5.8.3
Copyright (c) typescript authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/typescript.

 

Terms of the apache-2.0: 
Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License. 

Open Source Software Licensed under the bsd-new: 
-------------------------------------------------------------------- 
1. lxml-default
Copyright (c) lxml authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/lxml.

2. pypdf-default
Copyright (c) pypdf authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/pypdf.

3. go-querystring-1.1.0
Copyright (c) go-querystring authors.
You may obtain the source code and detailed information about this component at https://github.com/google/go-querystring.

4. speakingurl-14.0.1
Copyright (c) speakingurl authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/speakingurl.

5. markdown-default
Copyright (c) markdown authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/markdown.

6. source-map-0.6.1
Copyright (c) source-map authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/source-map.

7. serialize-javascript-6.0.2
Copyright (c) serialize-javascript authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/serialize-javascript.

8. golang-asm-0.15.1
Copyright (c) golang-asm authors.
You may obtain the source code and detailed information about this component at https://github.com/twitchyliquid64/golang-asm.

9. source-map-js-1.2.1
Copyright (c) source-map-js authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/source-map-js.

10. PyPDF2-default
Copyright (c) PyPDF2 authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/pypdf2.

11. fast-uri-3.0.6
Copyright (c) fast-uri authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/fast-uri.

12. mxj-1.8.4
Copyright (c) mxj authors.
You may obtain the source code and detailed information about this component at https://github.com/clbanning/mxj.

13. protobuf-default
Copyright (c) protobuf authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/protobuf.

14. @xtuc/ieee754-1.2.0
Copyright (c) @xtuc/ieee754 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@xtuc/ieee754.

 

Terms of the bsd-new: 
Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

Neither the name of the ORGANIZATION nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. 

Open Source Software Licensed under the bsd-simplified: 
-------------------------------------------------------------------- 
1. terser-5.43.1
Copyright (c) terser authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/terser.

2. estraverse-4.3.0
Copyright (c) estraverse authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/estraverse.

3. entities-4.5.0
Copyright (c) entities authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/entities.

 

Terms of the bsd-simplified: 
Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR
ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. 

Open Source Software Licensed under the bsd-zero: 
-------------------------------------------------------------------- 
1. tslib-2.8.1
Copyright (c) tslib authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/tslib.

 

Terms of the bsd-zero: 
Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE. 

Open Source Software Licensed under the isc: 
-------------------------------------------------------------------- 
1. graceful-fs-4.2.11
Copyright (c) graceful-fs authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/graceful-fs.

2. which-5.0.0
Copyright (c) which authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/which.

3. isexe-3.1.1
Copyright (c) isexe authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/isexe.

4. yaml-2.4.2
Copyright (c) yaml authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/yaml.

5. lru-cache-5.1.1
Copyright (c) lru-cache authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/lru-cache.

6. read-package-json-fast-4.0.0
Copyright (c) read-package-json-fast authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/read-package-json-fast.

7. minimatch-9.0.5
Copyright (c) minimatch authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/minimatch.

8. semver-6.3.1
Copyright (c) semver authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/semver.

9. npm-normalize-package-bin-4.0.0
Copyright (c) npm-normalize-package-bin authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/npm-normalize-package-bin.

10. picocolors-1.1.1
Copyright (c) picocolors authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/picocolors.

 

Terms of the isc: 
Permission to use, copy, modify, and/or distribute this software for any purpose
with or without fee is hereby granted, provided that the above copyright notice
and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS
OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF
THIS SOFTWARE.
 

Open Source Software Licensed under the mit: 
-------------------------------------------------------------------- 
1. @tsconfig/node22-22.0.2
Copyright (c) @tsconfig/node22 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@tsconfig/node22.

2. tinyglobby-0.2.14
Copyright (c) tinyglobby authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/tinyglobby.

3. jsesc-3.1.0
Copyright (c) jsesc authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/jsesc.

4. es-errors-1.3.0
Copyright (c) es-errors authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/es-errors.

5. brotli-1.1.0
Copyright (c) brotli authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/brotli.

6. undici-types-6.21.0
Copyright (c) undici-types authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/undici-types.

7. dayjs-1.11.10
Copyright (c) dayjs authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/dayjs.

8. @jridgewell/gen-mapping-0.3.12
Copyright (c) @jridgewell/gen-mapping authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@jridgewell/gen-mapping.

9. events-3.3.0
Copyright (c) events authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/events.

10. @types/sortablejs-1.15.8
Copyright (c) @types/sortablejs authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@types/sortablejs.

11. js-tokens-4.0.0
Copyright (c) js-tokens authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/js-tokens.

12. @vue/babel-helper-vue-transform-on-1.4.0
Copyright (c) @vue/babel-helper-vue-transform-on authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@vue/babel-helper-vue-transform-on.

13. @types/marked-5.0.2
Copyright (c) @types/marked authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@types/marked.

14. @types/tinycolor2-1.4.6
Copyright (c) @types/tinycolor2 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@types/tinycolor2.

15. xid-1.6.0
Copyright (c) xid authors.
You may obtain the source code and detailed information about this component at https://github.com/rs/xid.

16. lodash-es-4.17.21
Copyright (c) lodash-es authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/lodash-es.

17. go-runewidth-0.0.15
Copyright (c) go-runewidth authors.
You may obtain the source code and detailed information about this component at https://gitee.com/mirrors/go-runewidth.

18. marked-5.1.2
Copyright (c) marked authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/marked.

19. cast-1.7.1
Copyright (c) cast authors.
You may obtain the source code and detailed information about this component at https://github.com/spf13/cast.

20. antiword-default
Copyright (c) antiword authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/antiword.

21. cos-python-sdk-v5-default
Copyright (c) cos-python-sdk-v5 authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/cos-python-sdk-v5.

22. loader-runner-4.3.0
Copyright (c) loader-runner authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/loader-runner.

23. alien-signals-1.0.13
Copyright (c) alien-signals authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/alien-signals.

24. @webassemblyjs/wasm-parser-1.14.1
Copyright (c) @webassemblyjs/wasm-parser authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/wasm-parser.

25. textract-default
Copyright (c) textract authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/textract.

26. hasown-2.0.2
Copyright (c) hasown authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/hasown.

27. shebang-regex-3.0.0
Copyright (c) shebang-regex authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/shebang-regex.

28. @types/eslint-scope-3.7.7
Copyright (c) @types/eslint-scope authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@types/eslint-scope.

29. node-releases-2.0.19
Copyright (c) node-releases authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/node-releases.

30. combined-stream-1.0.8
Copyright (c) combined-stream authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/combined-stream.

31. ajv-formats-2.1.1
Copyright (c) ajv-formats authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/ajv-formats.

32. has-tostringtag-1.0.2
Copyright (c) has-tostringtag authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/has-tostringtag.

33. vscode-uri-3.1.0
Copyright (c) vscode-uri authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/vscode-uri.

34. npm-run-all2-7.0.2
Copyright (c) npm-run-all2 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/npm-run-all2.

35. tablewriter-0.0.5
Copyright (c) tablewriter authors.
You may obtain the source code and detailed information about this component at https://github.com/olekukonko/tablewriter.

36. cross-spawn-7.0.6
Copyright (c) cross-spawn authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/cross-spawn.

37. gin-1.10.0
Copyright (c) gin authors.
You may obtain the source code and detailed information about this component at https://github.com/donetkit/gin.

38. he-1.2.0
Copyright (c) he authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/he.

39. @babel/helper-optimise-call-expression-7.27.1
Copyright (c) @babel/helper-optimise-call-expression authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@babel/helper-optimise-call-expression.

40. debug-4.4.1
Copyright (c) debug authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/debug.

41. commander-2.20.3
Copyright (c) commander authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/commander.

42. mapstructure-1.4.3
Copyright (c) mapstructure authors.
You may obtain the source code and detailed information about this component at https://github.com/go-viper/mapstructure.

43. @vue/compiler-vue2-2.7.16
Copyright (c) @vue/compiler-vue2 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@vue/compiler-vue2.

44. escalade-3.2.0
Copyright (c) escalade authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/escalade.

45. conc-0.3.0
Copyright (c) conc authors.
You may obtain the source code and detailed information about this component at https://github.com/sourcegraph/conc.

46. @types/eslint-9.6.1
Copyright (c) @types/eslint authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@types/eslint.

47. go-httpheader-0.2.1
Copyright (c) go-httpheader authors.
You may obtain the source code and detailed information about this component at https://github.com/mozillazg/go-httpheader.

48. @webassemblyjs/wasm-opt-1.14.1
Copyright (c) @webassemblyjs/wasm-opt authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/wasm-opt.

49. @popperjs/core-2.11.8
Copyright (c) @popperjs/core authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@popperjs/core.

50. @webassemblyjs/wast-printer-1.14.1
Copyright (c) @webassemblyjs/wast-printer authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/wast-printer.

51. webpack-sources-3.3.3
Copyright (c) webpack-sources authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/webpack-sources.

52. @webassemblyjs/wasm-gen-1.14.1
Copyright (c) @webassemblyjs/wasm-gen authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/wasm-gen.

53. @babel/helper-validator-identifier-7.27.1
Copyright (c) @babel/helper-validator-identifier authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@babel/helper-validator-identifier.

54. merge-stream-2.0.0
Copyright (c) merge-stream authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/merge-stream.

55. logrus-1.9.3
Copyright (c) logrus authors.
You may obtain the source code and detailed information about this component at https://github.com/sirupsen/logrus.

56. path-key-3.1.1
Copyright (c) path-key authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/path-key.

57. gorm-1.25.12
Copyright (c) gorm authors.
You may obtain the source code and detailed information about this component at https://github.com/go-gorm/gorm.

58. fast-deep-equal-3.1.3
Copyright (c) fast-deep-equal authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/fast-deep-equal.

59. @webassemblyjs/utf8-1.13.2
Copyright (c) @webassemblyjs/utf8 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/utf8.

60. @babel/plugin-syntax-typescript-7.27.1
Copyright (c) @babel/plugin-syntax-typescript authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@babel/plugin-syntax-typescript.

61. mime-db-1.52.0
Copyright (c) mime-db authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/mime-db.

62. @webassemblyjs/floating-point-hex-parser-1.13.2
Copyright (c) @webassemblyjs/floating-point-hex-parser authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/floating-point-hex-parser.

63. hookable-5.5.3
Copyright (c) hookable authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/hookable.

64. pidtree-0.6.0
Copyright (c) pidtree authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/pidtree.

65. @types/lodash-es-4.17.12
Copyright (c) @types/lodash-es authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@types/lodash-es.

66. shebang-command-2.0.0
Copyright (c) shebang-command authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/shebang-command.

67. @webassemblyjs/wasm-edit-1.14.1
Copyright (c) @webassemblyjs/wasm-edit authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/wasm-edit.

68. convert-source-map-2.0.0
Copyright (c) convert-source-map authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/convert-source-map.

69. json-schema-traverse-1.0.0
Copyright (c) json-schema-traverse authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/json-schema-traverse.

70. @webassemblyjs/helper-buffer-1.14.1
Copyright (c) @webassemblyjs/helper-buffer authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/helper-buffer.

71. @babel/helper-module-imports-7.27.1
Copyright (c) @babel/helper-module-imports authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@babel/helper-module-imports.

72. update-browserslist-db-1.1.3
Copyright (c) update-browserslist-db authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/update-browserslist-db.

73. sugarss-5.0.0
Copyright (c) sugarss authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/sugarss.

74. uniseg-0.4.7
Copyright (c) uniseg authors.
You may obtain the source code and detailed information about this component at https://github.com/rivo/uniseg.

75. buffer-from-1.1.2
Copyright (c) buffer-from authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/buffer-from.

76. @vue/babel-plugin-jsx-1.4.0
Copyright (c) @vue/babel-plugin-jsx authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@vue/babel-plugin-jsx.

77. @webassemblyjs/helper-wasm-section-1.14.1
Copyright (c) @webassemblyjs/helper-wasm-section authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/helper-wasm-section.

78. dig-1.18.1
Copyright (c) dig authors.
You may obtain the source code and detailed information about this component at https://github.com/uber-go/dig.

79. go-isatty-0.0.20
Copyright (c) go-isatty authors.
You may obtain the source code and detailed information about this component at https://github.com/mattn/go-isatty.

80. randombytes-2.1.0
Copyright (c) randombytes authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/randombytes.

81. mistletoe-default
Copyright (c) mistletoe authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/mistletoe.

82. @jridgewell/trace-mapping-0.3.29
Copyright (c) @jridgewell/trace-mapping authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@jridgewell/trace-mapping.

83. @jridgewell/source-map-0.3.10
Copyright (c) @jridgewell/source-map authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@jridgewell/source-map.

84. ajv-keywords-5.1.0
Copyright (c) ajv-keywords authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/ajv-keywords.

85. memorystream-0.3.1
Copyright (c) memorystream authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/memorystream.

86. @vue/devtools-api-6.6.4
Copyright (c) @vue/devtools-api authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@vue/devtools-api.

87. go-1.1.12
Copyright (c) go authors.
You may obtain the source code and detailed information about this component at https://github.com/json-iterator/go.

88. supports-color-8.1.1
Copyright (c) supports-color authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/supports-color.

89. mime-types-2.1.35
Copyright (c) mime-types authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/mime-types.

90. enhanced-resolve-5.18.2
Copyright (c) enhanced-resolve authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/enhanced-resolve.

91. now-1.1.5
Copyright (c) now authors.
You may obtain the source code and detailed information about this component at https://github.com/jinzhu/now.

92. ajv-8.17.1
Copyright (c) ajv authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/ajv.

93. proxy-from-env-1.1.0
Copyright (c) proxy-from-env authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/proxy-from-env.

94. chrome-trace-event-1.0.4
Copyright (c) chrome-trace-event authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/chrome-trace-event.

95. axios-1.10.0
Copyright (c) axios authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/axios.

96. gotenv-1.6.0
Copyright (c) gotenv authors.
You may obtain the source code and detailed information about this component at https://github.com/subosito/gotenv.

97. brace-expansion-2.0.2
Copyright (c) brace-expansion authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/brace-expansion.

98. function-bind-1.1.2
Copyright (c) function-bind authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/function-bind.

99. @microsoft/fetch-event-source-2.0.1
Copyright (c) @microsoft/fetch-event-source authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@microsoft/fetch-event-source.

100. mxj-1.8.4
Copyright (c) mxj authors.
You may obtain the source code and detailed information about this component at https://github.com/clbanning/mxj.

101. schema-utils-4.3.2
Copyright (c) schema-utils authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/schema-utils.

102. muggle-string-0.4.1
Copyright (c) muggle-string authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/muggle-string.

103. go-urn-1.4.0
Copyright (c) go-urn authors.
You may obtain the source code and detailed information about this component at https://github.com/leodido/go-urn.

104. form-data-4.0.3
Copyright (c) form-data authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/form-data.

105. locales-0.14.1
Copyright (c) locales authors.
You may obtain the source code and detailed information about this component at https://gitee.com/mirrors/locales.

106. python-docx-default
Copyright (c) python-docx authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/python-docx.

107. less-loader-12.3.0
Copyright (c) less-loader authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/less-loader.

108. @jridgewell/sourcemap-codec-1.5.4
Copyright (c) @jridgewell/sourcemap-codec authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@jridgewell/sourcemap-codec.

109. universal-translator-0.18.1
Copyright (c) universal-translator authors.
You may obtain the source code and detailed information about this component at https://github.com/go-playground/universal-translator.

110. inflection-1.0.0
Copyright (c) inflection authors.
You may obtain the source code and detailed information about this component at https://github.com/jinzhu/inflection.

111. sass-embedded-1.70.0
Copyright (c) sass-embedded authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/sass-embedded.

112. @jridgewell/resolve-uri-3.1.2
Copyright (c) @jridgewell/resolve-uri authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@jridgewell/resolve-uri.

113. tinycolor2-1.6.0
Copyright (c) tinycolor2 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/tinycolor2.

114. pagefind-1.3.0
Copyright (c) pagefind authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/pagefind.

115. viper-1.20.1
Copyright (c) viper authors.
You may obtain the source code and detailed information about this component at https://github.com/spf13/viper.

116. delayed-stream-1.0.0
Copyright (c) delayed-stream authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/delayed-stream.

117. @webassemblyjs/helper-numbers-1.13.2
Copyright (c) @webassemblyjs/helper-numbers authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/helper-numbers.

118. nanoid-3.3.11
Copyright (c) nanoid authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/nanoid.

119. @babel/helper-compilation-targets-7.27.2
Copyright (c) @babel/helper-compilation-targets authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@babel/helper-compilation-targets.

120. @webassemblyjs/ast-1.14.1
Copyright (c) @webassemblyjs/ast authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/ast.

121. @types/estree-1.0.8
Copyright (c) @types/estree authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@types/estree.

122. uuid-1.6.0
Copyright (c) uuid authors.
You may obtain the source code and detailed information about this component at https://kojipkgs.fedoraproject.org//vol/fedora_koji_archive00/packages/uuid/1.6.0/2.fc8/src/uuid-1.6.0-2.fc8.src.rpm.

123. postgres-1.5.11
Copyright (c) postgres authors.
You may obtain the source code and detailed information about this component at https://github.com/go-gorm/postgres.

124. perfect-debounce-1.0.0
Copyright (c) perfect-debounce authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/perfect-debounce.

125. md5-simd-1.1.2
Copyright (c) md5-simd authors.
You may obtain the source code and detailed information about this component at https://github.com/minio/md5-simd.

126. ms-2.1.3
Copyright (c) ms authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/ms.

127. @babel/helper-module-transforms-7.27.3
Copyright (c) @babel/helper-module-transforms authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@babel/helper-module-transforms.

128. @types/json-schema-7.0.15
Copyright (c) @types/json-schema authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@types/json-schema.

129. multierr-1.11.0
Copyright (c) multierr authors.
You may obtain the source code and detailed information about this component at https://github.com/uber-go/multierr.

130. pflag-1.0.6
Copyright (c) pflag authors.
You may obtain the source code and detailed information about this component at https://github.com/php-toolkit/pflag.

131. @webassemblyjs/ieee754-1.13.2
Copyright (c) @webassemblyjs/ieee754 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/ieee754.

132. browserslist-4.25.1
Copyright (c) browserslist authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/browserslist.

133. asynckit-0.4.0
Copyright (c) asynckit authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/asynckit.

134. sortablejs-1.15.6
Copyright (c) sortablejs authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/sortablejs.

135. get-intrinsic-1.3.0
Copyright (c) get-intrinsic authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/get-intrinsic.

136. markdownify-default
Copyright (c) markdownify authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/markdownify.

137. go-json-0.10.5
Copyright (c) go-json authors.
You may obtain the source code and detailed information about this component at https://github.com/goccy/go-json.

138. path-browserify-1.0.1
Copyright (c) path-browserify authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/path-browserify.

139. terser-webpack-plugin-5.3.14
Copyright (c) terser-webpack-plugin authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/terser-webpack-plugin.

140. @babel/helpers-7.27.6
Copyright (c) @babel/helpers authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@babel/helpers.

141. @vue/babel-plugin-resolve-type-1.4.0
Copyright (c) @vue/babel-plugin-resolve-type authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@vue/babel-plugin-resolve-type.

142. json-parse-even-better-errors-4.0.0
Copyright (c) json-parse-even-better-errors authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/json-parse-even-better-errors.

143. csstype-3.1.3
Copyright (c) csstype authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/csstype.

144. @babel/helper-plugin-utils-7.27.1
Copyright (c) @babel/helper-plugin-utils authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@babel/helper-plugin-utils.

145. balanced-match-1.0.2
Copyright (c) balanced-match authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/balanced-match.

146. ansi-styles-6.2.1
Copyright (c) ansi-styles authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/ansi-styles.

147. go-humanize-1.0.1
Copyright (c) go-humanize authors.
You may obtain the source code and detailed information about this component at https://gitee.com/mirrors/go-humanize.

148. picomatch-4.0.2
Copyright (c) picomatch authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/picomatch.

149. @webassemblyjs/helper-wasm-bytecode-1.13.2
Copyright (c) @webassemblyjs/helper-wasm-bytecode authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@webassemblyjs/helper-wasm-bytecode.

150. @babel/code-frame-7.27.1
Copyright (c) @babel/code-frame authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@babel/code-frame.

151. tsx-4.8.1
Copyright (c) tsx authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/tsx.

152. superjson-2.2.2
Copyright (c) superjson authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/superjson.

153. require-from-string-2.0.2
Copyright (c) require-from-string authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/require-from-string.

154. json5-2.2.3
Copyright (c) json5 authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/json5.

155. sass-1.70.0
Copyright (c) sass authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/sass.

156. @vue/tsconfig-0.7.0
Copyright (c) @vue/tsconfig authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/@vue/tsconfig.

157. openai-default
Copyright (c) openai authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/openai.

158. urllib3-default
Copyright (c) urllib3 authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/urllib3.

159. beautifulsoup4-default
Copyright (c) beautifulsoup4 authors.
You may obtain the source code and detailed information about this component at https://pypi.org/project/beautifulsoup4.

160. pgpassfile-1.0.0
Copyright (c) pgpassfile authors.
You may obtain the source code and detailed information about this component at https://github.com/jackc/pgpassfile.

161. source-map-support-0.5.21
Copyright (c) source-map-support authors.
You may obtain the source code and detailed information about this component at https://www.npmjs.com/package/source-map-support.

 

Terms of the mit: 
Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.