// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: docreader.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReadConfig struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ChunkSize        int32                  `protobuf:"varint,1,opt,name=chunk_size,json=chunkSize,proto3" json:"chunk_size,omitempty"`                      // 分块大小
	ChunkOverlap     int32                  `protobuf:"varint,2,opt,name=chunk_overlap,json=chunkOverlap,proto3" json:"chunk_overlap,omitempty"`             // 分块重叠
	Separators       []string               `protobuf:"bytes,3,rep,name=separators,proto3" json:"separators,omitempty"`                                      // 分隔符
	EnableMultimodal bool                   `protobuf:"varint,4,opt,name=enable_multimodal,json=enableMultimodal,proto3" json:"enable_multimodal,omitempty"` // 多模态处理
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ReadConfig) Reset() {
	*x = ReadConfig{}
	mi := &file_docreader_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadConfig) ProtoMessage() {}

func (x *ReadConfig) ProtoReflect() protoreflect.Message {
	mi := &file_docreader_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadConfig.ProtoReflect.Descriptor instead.
func (*ReadConfig) Descriptor() ([]byte, []int) {
	return file_docreader_proto_rawDescGZIP(), []int{0}
}

func (x *ReadConfig) GetChunkSize() int32 {
	if x != nil {
		return x.ChunkSize
	}
	return 0
}

func (x *ReadConfig) GetChunkOverlap() int32 {
	if x != nil {
		return x.ChunkOverlap
	}
	return 0
}

func (x *ReadConfig) GetSeparators() []string {
	if x != nil {
		return x.Separators
	}
	return nil
}

func (x *ReadConfig) GetEnableMultimodal() bool {
	if x != nil {
		return x.EnableMultimodal
	}
	return false
}

// 从文件读取文档请求
type ReadFromFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileContent   []byte                 `protobuf:"bytes,1,opt,name=file_content,json=fileContent,proto3" json:"file_content,omitempty"` // 文件内容
	FileName      string                 `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`          // 文件名
	FileType      string                 `protobuf:"bytes,3,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`          // 文件类型
	ReadConfig    *ReadConfig            `protobuf:"bytes,4,opt,name=read_config,json=readConfig,proto3" json:"read_config,omitempty"`
	RequestId     string                 `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReadFromFileRequest) Reset() {
	*x = ReadFromFileRequest{}
	mi := &file_docreader_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadFromFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadFromFileRequest) ProtoMessage() {}

func (x *ReadFromFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_docreader_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadFromFileRequest.ProtoReflect.Descriptor instead.
func (*ReadFromFileRequest) Descriptor() ([]byte, []int) {
	return file_docreader_proto_rawDescGZIP(), []int{1}
}

func (x *ReadFromFileRequest) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

func (x *ReadFromFileRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ReadFromFileRequest) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *ReadFromFileRequest) GetReadConfig() *ReadConfig {
	if x != nil {
		return x.ReadConfig
	}
	return nil
}

func (x *ReadFromFileRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// 从URL读取文档请求
type ReadFromURLRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`     // 文档URL
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"` // 标题
	ReadConfig    *ReadConfig            `protobuf:"bytes,3,opt,name=read_config,json=readConfig,proto3" json:"read_config,omitempty"`
	RequestId     string                 `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReadFromURLRequest) Reset() {
	*x = ReadFromURLRequest{}
	mi := &file_docreader_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadFromURLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadFromURLRequest) ProtoMessage() {}

func (x *ReadFromURLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_docreader_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadFromURLRequest.ProtoReflect.Descriptor instead.
func (*ReadFromURLRequest) Descriptor() ([]byte, []int) {
	return file_docreader_proto_rawDescGZIP(), []int{2}
}

func (x *ReadFromURLRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ReadFromURLRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ReadFromURLRequest) GetReadConfig() *ReadConfig {
	if x != nil {
		return x.ReadConfig
	}
	return nil
}

func (x *ReadFromURLRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// 图片信息
type Image struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`                                    // 图片URL
	Caption       string                 `protobuf:"bytes,2,opt,name=caption,proto3" json:"caption,omitempty"`                            // 图片描述
	OcrText       string                 `protobuf:"bytes,3,opt,name=ocr_text,json=ocrText,proto3" json:"ocr_text,omitempty"`             // OCR提取的文本内容
	OriginalUrl   string                 `protobuf:"bytes,4,opt,name=original_url,json=originalUrl,proto3" json:"original_url,omitempty"` // 原始图片URL
	Start         int32                  `protobuf:"varint,5,opt,name=start,proto3" json:"start,omitempty"`                               // 图片在文本中的开始位置
	End           int32                  `protobuf:"varint,6,opt,name=end,proto3" json:"end,omitempty"`                                   // 图片在文本中的结束位置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Image) Reset() {
	*x = Image{}
	mi := &file_docreader_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_docreader_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_docreader_proto_rawDescGZIP(), []int{3}
}

func (x *Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Image) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *Image) GetOcrText() string {
	if x != nil {
		return x.OcrText
	}
	return ""
}

func (x *Image) GetOriginalUrl() string {
	if x != nil {
		return x.OriginalUrl
	}
	return ""
}

func (x *Image) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *Image) GetEnd() int32 {
	if x != nil {
		return x.End
	}
	return 0
}

type Chunk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"` // 块内容
	Seq           int32                  `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`        // 块在文档中的次序
	Start         int32                  `protobuf:"varint,3,opt,name=start,proto3" json:"start,omitempty"`    // 块在文档中的起始位置
	End           int32                  `protobuf:"varint,4,opt,name=end,proto3" json:"end,omitempty"`        // 块在文档中的结束位置
	Images        []*Image               `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`   // 块中包含的图片信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Chunk) Reset() {
	*x = Chunk{}
	mi := &file_docreader_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Chunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chunk) ProtoMessage() {}

func (x *Chunk) ProtoReflect() protoreflect.Message {
	mi := &file_docreader_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chunk.ProtoReflect.Descriptor instead.
func (*Chunk) Descriptor() ([]byte, []int) {
	return file_docreader_proto_rawDescGZIP(), []int{4}
}

func (x *Chunk) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Chunk) GetSeq() int32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *Chunk) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *Chunk) GetEnd() int32 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *Chunk) GetImages() []*Image {
	if x != nil {
		return x.Images
	}
	return nil
}

// 从URL读取文档响应
type ReadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chunks        []*Chunk               `protobuf:"bytes,1,rep,name=chunks,proto3" json:"chunks,omitempty"` // 文档分块
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`   // 错误信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReadResponse) Reset() {
	*x = ReadResponse{}
	mi := &file_docreader_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadResponse) ProtoMessage() {}

func (x *ReadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_docreader_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadResponse.ProtoReflect.Descriptor instead.
func (*ReadResponse) Descriptor() ([]byte, []int) {
	return file_docreader_proto_rawDescGZIP(), []int{5}
}

func (x *ReadResponse) GetChunks() []*Chunk {
	if x != nil {
		return x.Chunks
	}
	return nil
}

func (x *ReadResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

var File_docreader_proto protoreflect.FileDescriptor

const file_docreader_proto_rawDesc = "" +
	"\n" +
	"\x0fdocreader.proto\x12\tdocreader\"\x9d\x01\n" +
	"\n" +
	"ReadConfig\x12\x1d\n" +
	"\n" +
	"chunk_size\x18\x01 \x01(\x05R\tchunkSize\x12#\n" +
	"\rchunk_overlap\x18\x02 \x01(\x05R\fchunkOverlap\x12\x1e\n" +
	"\n" +
	"separators\x18\x03 \x03(\tR\n" +
	"separators\x12+\n" +
	"\x11enable_multimodal\x18\x04 \x01(\bR\x10enableMultimodal\"\xc9\x01\n" +
	"\x13ReadFromFileRequest\x12!\n" +
	"\ffile_content\x18\x01 \x01(\fR\vfileContent\x12\x1b\n" +
	"\tfile_name\x18\x02 \x01(\tR\bfileName\x12\x1b\n" +
	"\tfile_type\x18\x03 \x01(\tR\bfileType\x126\n" +
	"\vread_config\x18\x04 \x01(\v2\x15.docreader.ReadConfigR\n" +
	"readConfig\x12\x1d\n" +
	"\n" +
	"request_id\x18\x05 \x01(\tR\trequestId\"\x93\x01\n" +
	"\x12ReadFromURLRequest\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x126\n" +
	"\vread_config\x18\x03 \x01(\v2\x15.docreader.ReadConfigR\n" +
	"readConfig\x12\x1d\n" +
	"\n" +
	"request_id\x18\x04 \x01(\tR\trequestId\"\x99\x01\n" +
	"\x05Image\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x18\n" +
	"\acaption\x18\x02 \x01(\tR\acaption\x12\x19\n" +
	"\bocr_text\x18\x03 \x01(\tR\aocrText\x12!\n" +
	"\foriginal_url\x18\x04 \x01(\tR\voriginalUrl\x12\x14\n" +
	"\x05start\x18\x05 \x01(\x05R\x05start\x12\x10\n" +
	"\x03end\x18\x06 \x01(\x05R\x03end\"\x85\x01\n" +
	"\x05Chunk\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x10\n" +
	"\x03seq\x18\x02 \x01(\x05R\x03seq\x12\x14\n" +
	"\x05start\x18\x03 \x01(\x05R\x05start\x12\x10\n" +
	"\x03end\x18\x04 \x01(\x05R\x03end\x12(\n" +
	"\x06images\x18\x05 \x03(\v2\x10.docreader.ImageR\x06images\"N\n" +
	"\fReadResponse\x12(\n" +
	"\x06chunks\x18\x01 \x03(\v2\x10.docreader.ChunkR\x06chunks\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error2\x9f\x01\n" +
	"\tDocReader\x12I\n" +
	"\fReadFromFile\x12\x1e.docreader.ReadFromFileRequest\x1a\x17.docreader.ReadResponse\"\x00\x12G\n" +
	"\vReadFromURL\x12\x1d.docreader.ReadFromURLRequest\x1a\x17.docreader.ReadResponse\"\x00B5Z3github.com/Tencent/WeKnora/internal/docreader/protob\x06proto3"

var (
	file_docreader_proto_rawDescOnce sync.Once
	file_docreader_proto_rawDescData []byte
)

func file_docreader_proto_rawDescGZIP() []byte {
	file_docreader_proto_rawDescOnce.Do(func() {
		file_docreader_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_docreader_proto_rawDesc), len(file_docreader_proto_rawDesc)))
	})
	return file_docreader_proto_rawDescData
}

var file_docreader_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_docreader_proto_goTypes = []any{
	(*ReadConfig)(nil),          // 0: docreader.ReadConfig
	(*ReadFromFileRequest)(nil), // 1: docreader.ReadFromFileRequest
	(*ReadFromURLRequest)(nil),  // 2: docreader.ReadFromURLRequest
	(*Image)(nil),               // 3: docreader.Image
	(*Chunk)(nil),               // 4: docreader.Chunk
	(*ReadResponse)(nil),        // 5: docreader.ReadResponse
}
var file_docreader_proto_depIdxs = []int32{
	0, // 0: docreader.ReadFromFileRequest.read_config:type_name -> docreader.ReadConfig
	0, // 1: docreader.ReadFromURLRequest.read_config:type_name -> docreader.ReadConfig
	3, // 2: docreader.Chunk.images:type_name -> docreader.Image
	4, // 3: docreader.ReadResponse.chunks:type_name -> docreader.Chunk
	1, // 4: docreader.DocReader.ReadFromFile:input_type -> docreader.ReadFromFileRequest
	2, // 5: docreader.DocReader.ReadFromURL:input_type -> docreader.ReadFromURLRequest
	5, // 6: docreader.DocReader.ReadFromFile:output_type -> docreader.ReadResponse
	5, // 7: docreader.DocReader.ReadFromURL:output_type -> docreader.ReadResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_docreader_proto_init() }
func file_docreader_proto_init() {
	if File_docreader_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_docreader_proto_rawDesc), len(file_docreader_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_docreader_proto_goTypes,
		DependencyIndexes: file_docreader_proto_depIdxs,
		MessageInfos:      file_docreader_proto_msgTypes,
	}.Build()
	File_docreader_proto = out.File
	file_docreader_proto_goTypes = nil
	file_docreader_proto_depIdxs = nil
}
