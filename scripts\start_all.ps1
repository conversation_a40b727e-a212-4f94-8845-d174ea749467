# WeKnora PowerShell Startup Script
Write-Host "WeKnora Startup Script for Windows (PowerShell)" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Function to write colored output
function Write-Info($message) {
    Write-Host "[INFO] $message" -ForegroundColor Blue
}

function Write-Success($message) {
    Write-Host "[SUCCESS] $message" -ForegroundColor Green
}

function Write-Warning($message) {
    Write-Host "[WARNING] $message" -ForegroundColor Yellow
}

function Write-Error($message) {
    Write-Host "[ERROR] $message" -ForegroundColor Red
}

# Check if Docker is installed
Write-Info "Checking Docker installation..."

try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Docker found: $dockerVersion"
    } else {
        throw "Docker command failed"
    }
} catch {
    Write-Error "Docker is not installed or not accessible."
    Write-Host "Please install Docker Desktop and ensure it's running."
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Docker is running
Write-Info "Checking if Docker is running..."
try {
    docker info | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Docker is running successfully."
    } else {
        throw "Docker info failed"
    }
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop."
    Read-Host "Press Enter to exit"
    exit 1
}

# Check for .env file
Write-Info "Checking environment configuration..."
if (-not (Test-Path ".env")) {
    Write-Warning ".env file does not exist."
    if (Test-Path ".env.example") {
        Write-Info "Creating .env from .env.example"
        Copy-Item ".env.example" ".env"
        Write-Success ".env file created from template."
    } else {
        Write-Error ".env.example template not found."
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Info ".env file exists."
}

# Parse command line arguments
$stopServices = $false
if ($args.Length -gt 0) {
    switch ($args[0]) {
        "-s" { $stopServices = $true }
        "--stop" { $stopServices = $true }
        "-h" { 
            Write-Host "Usage: .\scripts\start_all.ps1 [options]"
            Write-Host "Options:"
            Write-Host "  -s, --stop    Stop all services"
            Write-Host "  -h, --help    Show this help"
            exit 0
        }
        "--help" { 
            Write-Host "Usage: .\scripts\start_all.ps1 [options]"
            Write-Host "Options:"
            Write-Host "  -s, --stop    Stop all services"
            Write-Host "  -h, --help    Show this help"
            exit 0
        }
    }
}

# Disable Docker credential helper to avoid credential issues
$env:DOCKER_CONFIG = $null

if ($stopServices) {
    Write-Info "Stopping Docker containers..."
    
    try {
        docker compose down --remove-orphans
        if ($LASTEXITCODE -eq 0) {
            Write-Success "All Docker containers stopped successfully."
        } else {
            Write-Error "Failed to stop Docker containers."
            exit 1
        }
    } catch {
        Write-Error "Error stopping containers: $_"
        exit 1
    }
} else {
    Write-Info "Starting Docker containers..."
    
    try {
        docker compose up --build -d
        if ($LASTEXITCODE -eq 0) {
            Write-Success "All Docker containers started successfully."
            Write-Host ""
            Write-Success "Services are available at:"
            Write-Host "  - Frontend: http://localhost" -ForegroundColor Green
            Write-Host "  - API: http://localhost:8080" -ForegroundColor Green
            Write-Host "  - Jaeger Tracing: http://localhost:16686" -ForegroundColor Green
            Write-Host ""
            Write-Info "Container status:"
            docker compose ps
        } else {
            Write-Error "Failed to start Docker containers."
            Write-Host ""
            Write-Info "Troubleshooting tips:"
            Write-Host "1. Make sure Docker Desktop is fully started"
            Write-Host "2. Try restarting Docker Desktop"
            Write-Host "3. Check if ports 80, 8080, 5432, 6379 are available"
            Write-Host "4. Run 'docker system prune' to clean up Docker resources"
            exit 1
        }
    } catch {
        Write-Error "Error starting containers: $_"
        exit 1
    }
}

Write-Host ""
Write-Success "Script completed successfully."
Read-Host "Press Enter to exit"
