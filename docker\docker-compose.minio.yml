version: "3.8"

services:
  minio:
    image: minio/minio:latest
    container_name: WeKnora-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio-data:/data
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY_ID}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_ACCESS_KEY}
    command: server --console-address ":9001" /data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - WeKnora-network

volumes:
  minio-data:

networks:
  WeKnora-network:
    external: true 