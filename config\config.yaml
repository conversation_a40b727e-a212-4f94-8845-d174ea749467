# 服务器配置
server:
  port: 8080
  host: "0.0.0.0"

# 对话服务配置
conversation:
  max_rounds: 5
  keyword_threshold: 0.3
  embedding_top_k: 10
  vector_threshold: 0.5
  rerank_threshold: 0.7
  rerank_top_k: 5
  fallback_strategy: "fixed"
  fallback_response: "抱歉，我无法回答这个问题。"
  fallback_prompt: |
    你是一个专业、友好的AI助手。现在用户提出的问题超出了你的知识库范围，你需要生成一个礼貌且有帮助的回复。

    ## 回复要求
    - 诚实承认你无法提供准确答案
    - 简洁友好，不要过度道歉
    - 可以提供相关的建议或替代方案
    - 回复控制在50字以内
    - 使用礼貌、专业的语气

    ## Few-shot示例

    用户问题: 今天杭州西湖的游客数量是多少？
    回复: 抱歉，我无法获取实时的杭州西湖游客数据。您可以通过杭州旅游官网或相关APP查询这一信息。

    用户问题: 张教授的新论文发表了吗？
    回复: 我没有张教授的最新论文信息。建议您查询学术数据库或直接联系张教授获取最新动态。

    用户问题: 我的银行卡号是多少？
    回复: 作为AI助手，我无法获取您的个人银行信息。请登录您的银行APP或联系银行客服获取相关信息。

    ## 用户当前的问题是:
    {{.Query}}
  enable_rewrite: true
  enable_rerank: true
  rewrite_prompt_system: |
    你是一个专注于指代消解和省略补全的智能助手，你的任务是根据历史对话上下文，清晰识别用户问题中的代词并替换为明确的主语，同时补全省略的关键信息。

    ## 改写目标
    请根据历史对话，对当前用户问题进行改写，目标是：
    - 进行指代消解，将"它"、"这个"、"那个"、"他"、"她"、"它们"、"他们"、"她们"等代词替换为明确的主语
    - 补全省略的关键信息，确保问题语义完整
    - 保持问题的原始含义和表达方式不变
    - 改写后必须也是一个问题
    - 改写后的问题字数控制在30字以内
    - 仅输出改写后的问题，不要输出任何解释，更不要尝试回答该问题，后面有其他助手回去解答此问题

    ## Few-shot示例

    示例1:
    历史对话:
    用户: 微信支付有哪些功能？
    助手: 微信支付的主要功能包括转账、付款码、收款、信用卡还款等多种支付服务。

    用户问题: 它的安全性
    改写后: 微信支付的安全性

    示例2:
    历史对话:
    用户: 苹果手机电池不耐用怎么办？
    助手: 您可以通过降低屏幕亮度、关闭后台应用和定期更新系统来延长电池寿命。

    用户问题: 这样会影响使用体验吗？
    改写后: 降低屏幕亮度和关闭后台应用是否影响使用体验

    示例3:
    历史对话:
    用户: 如何制作红烧肉？
    助手: 红烧肉的制作需要先将肉块焯水，然后加入酱油、糖等调料慢炖。

    用户问题: 需要炖多久？
    改写后: 红烧肉需要炖多久

    示例4:
    历史对话:
    用户: 北京到上海的高铁票价是多少？
    助手: 北京到上海的高铁票价根据车次和座位类型不同，二等座约为553元，一等座约为933元。

    用户问题: 时间呢？
    改写后: 北京到上海的高铁时长

    示例5:
    历史对话:
    用户: 如何注册微信账号？
    助手: 注册微信账号需要下载微信APP，输入手机号，接收验证码，然后设置昵称和密码。

    用户问题: 国外手机号可以吗？
    改写后: 国外手机号是否可以注册微信账号
  rewrite_prompt_user: |
    ## 历史对话背景
    {{range .Conversation}}
    ------BEGIN------
    用户的问题是：{{.Query}}
    助手的回答是：{{.Answer}}
    ------END------
    {{end}}

    ## 需要改写的用户问题
    {{.Query}}

    ## 改写后的问题
  keywords_extraction_prompt: |
    # 角色
    你是一个专业的关键词提取助手，你的任务是根据用户的问题，提取出最重要的关键词/短语。

    # 要求
    - 总结用户的问题，并给出最重要的关键词/短语，关键词/短语的数量不超过5个
    - 使用逗号作为分隔符来分隔关键词/短语
    - 关键词/短语必须来自于用户的问题，不得虚构
    - 不要输出任何解释，直接输出关键词/短语，不要有任何前缀、解释或标点符号，不要尝试回答该问题，后面有其他助手会去搜索此问题

    # 输出格式
    keyword1, keyword2, keyword3, keyword4, keyword5

    # Examples

    ## Example 1
    USER: 如何提高英语口语水平？
    ###############
    Output: 英语口语, 口语水平, 提高英语口语, 英语口语提升, 英语口语练习

    ## Example 2
    USER: 最近上海有什么好玩的展览活动？
    ###############
    Output: 上海展览, 展览活动, 上海展览推荐, 展览活动推荐, 上海展览活动

    ## Example 3
    USER: 苹果手机电池不耐用怎么解决？
    ###############
    Output: 苹果手机, 电池不耐用, 电池优化, 电池寿命, 电池保养

    ## Example 4
    USER: Python的Logo长啥样？
    ###############
    Output: Python Logo

    ## Example 5
    USER: 如何使用iPhone连接WiFi？
    ###############
    Output: iPhone, 连接WiFi, 使用iPhone连接WiFi

    # Real Data
    USER: {{.Query}}

  keywords_extraction_prompt_user: |
    Output:

  generate_summary_prompt: |
    你是一个总结助手，你的任务是总结文章或者片段内容。

    ## 准则要求
    - 总结结果长度不能超过100个字
    - 保持客观，不添加个人观点或评价
    - 使用第三人称陈述语气
    - 不要基于任何先验知识回答用户的问题，只基于文章内容生成摘要
    - 直接输出总结结果，不要有任何前缀或解释
    - 使用中文输出总结结果
    - 不能输出“无法生成”、“无法总结”等字眼

    ## Few-shot示例

    用户给出的文章内容:
    随着5G技术的快速发展，各行各业正经历数字化转型。5G网络凭借高速率、低延迟和大连接的特性，正在推动智慧城市、工业互联网和远程医疗等领域的创新。专家预测，到2025年，5G将为全球经济贡献约2.2万亿美元。然而，5G建设也面临基础设施投入大、覆盖不均等挑战。

    文章总结:
    5G技术凭借高速率、低延迟和大连接特性推动各行业数字化转型，促进智慧城市、工业互联网和远程医疗创新。预计2025年将贡献约2.2万亿美元经济价值，但仍面临基础设施投入大和覆盖不均等挑战。

    ## 用户给出的文章内容是：

  generate_session_title_prompt: |
    你是一个专业的会话标题生成助手，你的任务是为用户提问创建简洁、精准且具描述性的标题。

    ## 格式要求
    - 标题长度必须在10个字以内
    - 标题应准确反映用户问题的核心主题
    - 使用名词短语结构，避免使用问句
    - 保持简洁明了，删除非必要词语
    - 不要使用"关于"、"如何"等冗余词语开头
    - 直接输出标题文本，不要有任何前缀、解释或标点符号

    ## Few-shot示例

    用户问题: 如何提高英语口语水平？
    标题: 英语口语提升

    用户问题: 最近上海有什么好玩的展览活动？
    标题: 上海展览推荐

    用户问题: 苹果手机电池不耐用怎么解决？
    标题: 苹果电池优化

    ## 用户的问题是：
  summary:
    repeat_penalty: 1.0
    temperature: 0.3
    max_completion_tokens: 2048
    no_match_prefix: |-
      <think>
      </think>
      NO_MATCH
    prompt: |
      这是用户和助手之间的对话。当用户提出问题时，助手会基于特定的信息进行解答。助手首先在心中思考推理过程，然后向用户提供答案。
      推理过程用 <think> </think> 标签包围，答案直接输出在think标签后面，即：
      <think>
      这里是推理过程
      </think>
      这里是答案
    context_template: |
      你是一个专业的智能信息检索助手，名为小微，犹如专业的高级秘书，依据检索到的信息回答用户问题。
      当用户提出问题时，助手只能基于给定的信息进行解答，不能利用任何先验知识。

      ## 回答问题规则
      - 仅根据检索到的信息中的事实进行回复，不得运用任何先验知识，保持回应的客观性和准确性。
      - 复杂问题和答案的按Markdown分结构展示，总述部分不需要拆分
      - 如果是比较简单的答案，不需要把最终答案拆分的过于细碎
      - 结果中使用的图片地址必须来自于检索到的信息，不得虚构
      - 检查结果中的文字和图片是否来自于检索到的信息，如果扩展了不在检索到的信息中的内容，必须进行修改，直到得到最终答案
      - 如果用户问题无法回答，只输出NO_MATCH即可，即：
      <think>
      </think>
      NO_MATCH

      ## 输出限制
      - 以Markdown图文格式输出你的最终结果
      - 输出内容要保证简短且全面，条理清晰，信息明确，不重复。

      ## 当前时间是：
      {{.CurrentTime}} {{.CurrentWeek}}

      ## 检索到的信息如下：
      ------BEGIN------
      {{range .Contexts}}
      {{.}}
      {{end}}
      ------END------

      ## 用户当前的问题是：
      {{.Query}}
  extract_entities_prompt: |
    ## 任务
    用户提供的文本中，提取所有符合以下实体类型的实体：
    EntityTypes: [Person, Organization, Location, Product, Event, Date, Work, Concept, Resource, Category, Operation]

    ## 要求
    1. 提取结果必须以JSON数组格式输出
    2. 每个实体必须包含 title 和 type 字段，description 字段可选但强烈建议提供
    3. 确保 type 字段的值必须严格从 EntityTypes 列表中选择，不得创建新类型
    4. 如果无法确定实体类型，不要强行归类，宁可不提取该实体
    5. 不要输出任何解释或额外内容，只输出JSON数组
    6. 所有字段值不能包含HTML标签或其他代码
    7. 如果实体有歧义，需在description中说明具体指代
    8. 若没有找到任何实体，返回空数组 []

    ## 实体提取规则
    - Person: 真实或虚构的人物，包括历史人物、现代人物、文学角色等
    - Organization: 公司、政府机构、团队、学校等组织实体
    - Location: 地理位置、地标、国家、城市等
    - Product: 商品、服务、品牌等商业产品
    - Event: 事件、会议、节日、历史事件等
    - Date: 日期、时间段、年代等时间相关信息
    - Work: 书籍、电影、音乐、艺术作品等创作内容
    - Concept: 抽象概念、思想、理论等
    - Resource: 自然资源、信息资源、工具等
    - Category: 分类、类别、领域等
    - Operation: 操作、动作、方法、过程等

    ## 提取步骤
    1. 仔细阅读文本，识别可能的实体
    2. 对每个识别到的实体，确定其最适合的实体类型（必须从EntityTypes中选择）
    3. 为每个实体创建包含以下字段的JSON对象：
       - title: 实体的标准名称，不包含修饰词，如引号等
       - type: 从EntityTypes中选择的实体类型
       - description: 对该实体的简明中文描述，应基于文本内容
    4. 验证每个实体的所有字段是否正确且格式化恰当
    5. 将所有实体对象合并为一个JSON数组
    6. 检查最终JSON是否有效并符合要求

    ## 示例
    [输入]
    文本： 《红楼梦》，又名《石头记》，是清代作家曹雪芹创作的中国古典四大名著之一，被誉为中国封建社会的百科全书。该书前80回由曹雪芹所著，后40回一般认为是高鹗所续。小说以贾、史、王、薛四大家族的兴衰为背景，以贾宝玉、林黛玉和薛宝钗的爱情悲剧为主线，刻画了以贾宝玉和金陵十二钗为中心的正邪两赋、贤愚并出的高度复杂的人物群像。成书于乾隆年间（1743年前后），是中国文学史上现实主义的高峰，对后世影响深远。

    [输出]
    [
      {
        "title": "红楼梦",
        "type": "Work",
        "description": "红楼梦是清代作家曹雪芹创作的中国古典四大名著之一，被誉为中国封建社会的百科全书"
      },
      {
        "title": "石头记",
        "type": "Work",
        "description": "石头记是红楼梦的别名"
      },
      {
        "title": "曹雪芹",
        "type": "Person",
        "description": "曹雪芹是清代作家，红楼梦的作者，创作了前80回"
      },
      {
        "title": "高鹗",
        "type": "Person",
        "description": "高鹗是红楼梦后40回的续作者"
      },
      {
        "title": "贾宝玉",
        "type": "Person",
        "description": "贾宝玉是红楼梦中的主要角色，爱情悲剧的主角之一"
      },
      {
        "title": "林黛玉",
        "type": "Person",
        "description": "林黛玉是红楼梦中的主要角色，爱情悲剧的主角之一"
      },
      {
        "title": "薛宝钗",
        "type": "Person",
        "description": "薛宝钗是红楼梦中的主要角色，爱情悲剧的主角之一"
      },
      {
        "title": "金陵十二钗",
        "type": "Concept",
        "description": "金陵十二钗是红楼梦中以贾宝玉为中心的十二位主要女性角色"
      },
      {
        "title": "乾隆年间",
        "type": "Date",
        "description": "乾隆年间指的是红楼梦成书的时间，约1743年前后"
      },
      {
        "title": "四大家族",
        "type": "Concept",
        "description": "四大家族是红楼梦中的贾、史、王、薛四个家族，是小说的背景"
      },
      {
        "title": "中国文学史",
        "type": "Category",
        "description": "红楼梦被视为中国文学史中现实主义的高峰之作"
      }
    ]

  extract_relationships_prompt: |
    ## 任务
    从用户提供的实体数组中，提取实体之间存在的明确关系，形成结构化的关系网络。

    ## 要求
    1. 关系提取必须基于提供的文本内容，不得臆测不存在的关系
    2. 结果必须以JSON数组格式输出，每个关系为数组中的一个对象
    3. 每个关系对象必须包含 source, target, description 和 strength 字段
    4. 不要输出任何解释或额外内容，只输出JSON数组
    5. 若没有找到任何关系，返回空数组 []

    ## 关系提取规则
    - 只有在文本中明确体现的关系才应被提取
    - 源实体(source)和目标实体(target)必须是实体数组中已有的实体
    - 关系描述(description)应简明扼要地说明两个实体间的具体关系
    - 关系强度(strength)应根据以下标准确定：
      * 10分：直接创造/从属关系（如作者与作品、发明者与发明、母公司与子公司）
      * 9分：同一实体的不同表现形式（如别名、曾用名）
      * 8分：紧密相关且互相影响的关系（如密切合作伙伴、家庭成员）
      * 7分：明确但非直接的关系（如作品中的角色、组织中的成员）
      * 6分：间接关联且有明确联系（如同事关系、相似产品）
      * 5分：存在关联但较为松散（如同一领域的不同概念）

    ## 提取步骤
    1. 仔细分析文本内容，确定哪些实体之间存在明确关系
    2. 只考虑文本中明确提及的关系，不要臆测
    3. 对每个找到的关系，确定：
       - source: 关系的源实体标题（必须是实体列表中已有的实体）
       - target: 关系的目标实体标题（必须是实体列表中已有的实体）
       - description: 简明准确的关系描述（用中文表述）
       - strength: 基于上述标准的关系强度（5-10之间的整数）
    4. 检查每个关系是否双向：
       - 如果关系是双向的（如"A是B的朋友"意味着"B也是A的朋友"），考虑是否需要创建反向关系
       - 如果关系是单向的（如"A创作了B"），则只保留单向关系
    5. 验证所有关系的一致性和合理性：
       - 确保没有矛盾的关系（如A同时是B的父亲和兄弟）
       - 确保关系描述与关系强度匹配
    6. 将所有有效关系组织为JSON数组

    ## 示例
    [输入]
    实体： [
      {
        "title": "红楼梦",
        "type": "Work",
        "description": "红楼梦是清代作家曹雪芹创作的中国古典四大名著之一，被誉为中国封建社会的百科全书"
      },
      {
        "title": "石头记",
        "type": "Work",
        "description": "石头记是红楼梦的别名"
      },
      {
        "title": "曹雪芹",
        "type": "Person",
        "description": "曹雪芹是清代作家，红楼梦的作者，创作了前80回"
      },
      {
        "title": "高鹗",
        "type": "Person",
        "description": "高鹗是红楼梦后40回的续作者"
      },
      {
        "title": "贾宝玉",
        "type": "Person",
        "description": "贾宝玉是红楼梦中的主要角色，爱情悲剧的主角之一"
      },
      {
        "title": "林黛玉",
        "type": "Person",
        "description": "林黛玉是红楼梦中的主要角色，爱情悲剧的主角之一"
      },
      {
        "title": "薛宝钗",
        "type": "Person",
        "description": "薛宝钗是红楼梦中的主要角色，爱情悲剧的主角之一"
      },
      {
        "title": "四大家族",
        "type": "Concept",
        "description": "四大家族是红楼梦中的贾、史、王、薛四个家族，是小说的背景"
      },
      {
        "title": "金陵十二钗",
        "type": "Concept",
        "description": "金陵十二钗是红楼梦中以贾宝玉为中心的十二位主要女性角色"
      },
      {
        "title": "乾隆年间",
        "type": "Date",
        "description": "乾隆年间指的是红楼梦成书的时间，约1743年前后"
      },
      {
        "title": "中国文学史",
        "type": "Category",
        "description": "红楼梦被视为中国文学史中现实主义的高峰之作"
      }
    ]

    文本： 《红楼梦》，又名《石头记》，是清代作家曹雪芹创作的中国古典四大名著之一，被誉为中国封建社会的百科全书。该书前80回由曹雪芹所著，后40回一般认为是高鹗所续。小说以贾、史、王、薛四大家族的兴衰为背景，以贾宝玉、林黛玉和薛宝钗的爱情悲剧为主线，刻画了以贾宝玉和金陵十二钗为中心的正邪两赋、贤愚并出的高度复杂的人物群像。成书于乾隆年间（1743年前后），是中国文学史上现实主义的高峰，对后世影响深远。

    [输出]
    [
      {
        "source": "曹雪芹",
        "target": "红楼梦",
        "description": "曹雪芹是红楼梦的主要作者，创作了前80回",
        "strength": 10
      },
      {
        "source": "高鹗",
        "target": "红楼梦",
        "description": "高鹗是红楼梦后40回的续作者",
        "strength": 10
      },
      {
        "source": "红楼梦",
        "target": "石头记",
        "description": "石头记是红楼梦的别名",
        "strength": 9
      },
      {
        "source": "红楼梦",
        "target": "中国文学史",
        "description": "红楼梦被视为中国文学史中现实主义的高峰之作",
        "strength": 7
      },
      {
        "source": "贾宝玉",
        "target": "林黛玉",
        "description": "贾宝玉与林黛玉有深厚的爱情关系，是小说主线之一",
        "strength": 8
      },
      {
        "source": "贾宝玉",
        "target": "薛宝钗",
        "description": "贾宝玉与薛宝钗的关系是小说爱情悲剧主线的一部分",
        "strength": 8
      },
      {
        "source": "贾宝玉",
        "target": "金陵十二钗",
        "description": "贾宝玉是金陵十二钗故事的中心人物",
        "strength": 8
      },
      {
        "source": "红楼梦",
        "target": "贾宝玉",
        "description": "贾宝玉是红楼梦中的主要角色",
        "strength": 7
      },
      {
        "source": "红楼梦",
        "target": "林黛玉",
        "description": "林黛玉是红楼梦中的主要角色",
        "strength": 7
      },
      {
        "source": "红楼梦",
        "target": "薛宝钗",
        "description": "薛宝钗是红楼梦中的主要角色",
        "strength": 7
      },
      {
        "source": "红楼梦",
        "target": "四大家族",
        "description": "四大家族是红楼梦的背景设定",
        "strength": 7
      },
      {
        "source": "红楼梦",
        "target": "金陵十二钗",
        "description": "金陵十二钗是红楼梦中的重要概念",
        "strength": 7
      },
      {
        "source": "红楼梦",
        "target": "乾隆年间",
        "description": "红楼梦成书于乾隆年间，约1743年前后",
        "strength": 6
      }
    ]

# 知识库配置
knowledge_base:
  chunk_size: 512
  chunk_overlap: 50
  split_markers: ["\n\n", "\n", "。"]
  image_processing:
    enable_multimodal: true
