@echo off
echo Testing Docker installation...

:: Check if Docker is installed
set "DOCKER_CMD=docker"

:: First try the standard PATH
where docker >nul 2>&1
if %errorlevel% equ 0 (
    echo Docker found in PATH
    goto :docker_found
)

:: Try common Docker Desktop installation paths
if exist "C:\Program Files\Docker\Docker\resources\bin\docker.exe" (
    set "DOCKER_CMD=C:\Program Files\Docker\Docker\resources\bin\docker.exe"
    echo Docker found at: %DOCKER_CMD%
    goto :docker_found
)

echo Docker not found
exit /b 1

:docker_found
echo Testing Docker command...
"%DOCKER_CMD%" --version
if %errorlevel% neq 0 (
    echo ERROR: Docker command failed
    exit /b 1
)

echo Testing Docker info...
"%DOCKER_CMD%" info
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running or not accessible
    exit /b 1
)

echo Docker test completed successfully!
pause
