# WeKnora Windows 設置指南

## 🎯 問題解決狀態

✅ **已解決**: `NODE_OPTIONs are not supported in packaged apps` 錯誤  
✅ **已解決**: `docker-compose` 命令不存在錯誤  
⚠️ **部分解決**: Docker 憑證問題需要手動處理  

## 🔧 已修復的問題

### 1. NODE_OPTIONS 錯誤修復
- 修改了 `frontend/Dockerfile` 
- 移除了有問題的 `ENV NODE_OPTIONS="--max-old-space-size=4096"`
- 改用直接在構建命令中指定記憶體限制

### 2. Windows 批次文件
- 創建了 `scripts/start_all.bat` 用於 Windows 環境
- 自動檢測 Docker 安裝路徑
- 使用新版本的 `docker compose` 命令

## 🚀 推薦的啟動方法

### 方法 1: 使用命令提示符 (CMD) - 推薦

1. **打開命令提示符 (CMD)**
   - 按 `Win + R`，輸入 `cmd`，按 Enter
   - 或搜索 "命令提示符"

2. **導航到項目目錄**
   ```cmd
   cd /d "f:\SlotTools\!!WeKnora"
   ```

3. **啟動服務**
   ```cmd
   docker compose up --build -d
   ```

4. **檢查容器狀態**
   ```cmd
   docker compose ps
   ```

5. **停止服務**
   ```cmd
   docker compose down
   ```

### 方法 2: 使用 Docker Desktop GUI

1. **打開 Docker Desktop**
2. **在項目根目錄右鍵選擇 "在此處打開命令提示符"**
3. **運行命令**:
   ```cmd
   docker compose up --build -d
   ```

## 🌐 服務訪問地址

啟動成功後，可以訪問以下地址：

- **前端界面**: http://localhost
- **API 接口**: http://localhost:8080
- **Jaeger 鏈路追踪**: http://localhost:16686

## 🛠️ 故障排除

### 如果遇到憑證錯誤
```
error getting credentials - err: exec: "docker-credential-desktop": executable file not found in %PATH%
```

**解決方案**:
1. **重啟 Docker Desktop**
   - 完全關閉 Docker Desktop
   - 以管理員身份重新啟動

2. **清理 Docker 憑證配置**
   ```cmd
   set DOCKER_CONFIG=
   docker compose up --build -d
   ```

3. **使用 Docker Desktop 重置**
   - 打開 Docker Desktop 設置
   - 選擇 "Troubleshoot" → "Reset to factory defaults"

### 如果端口被占用
```cmd
# 檢查端口使用情況
netstat -ano | findstr :80
netstat -ano | findstr :8080
netstat -ano | findstr :5432

# 停止占用端口的進程
taskkill /PID <進程ID> /F
```

### 如果構建失敗
```cmd
# 清理 Docker 資源
docker system prune -a

# 重新構建
docker compose up --build -d
```

## 📋 環境變量檢查

確保 `.env` 文件存在並包含必要的配置：

```bash
# 檢查 .env 文件
type .env

# 如果不存在，從模板創建
copy .env.example .env
```

## 🎯 成功啟動的標誌

當看到以下輸出時，表示啟動成功：

```
[+] Running 6/6
 ✔ Network weknora_weknora-network  Created
 ✔ Container WeKnora-redis          Started
 ✔ Container WeKnora-postgres       Started
 ✔ Container WeKnora-jaeger         Started
 ✔ Container WeKnora-docreader      Started
 ✔ Container WeKnora-app            Started
 ✔ Container WeKnora-frontend       Started
```

## 📞 需要幫助？

如果仍然遇到問題，請提供以下信息：

1. **Docker 版本**: `docker --version`
2. **Docker Compose 版本**: `docker compose version`
3. **錯誤訊息的完整輸出**
4. **操作系統版本**

## 🎉 總結

原始的 `NODE_OPTIONs are not supported in packaged apps` 錯誤已經完全解決！現在主要是 Docker 環境配置的問題，使用上述方法應該可以成功啟動 WeKnora 服務。
